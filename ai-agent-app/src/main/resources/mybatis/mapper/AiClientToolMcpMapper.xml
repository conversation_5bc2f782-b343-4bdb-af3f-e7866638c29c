<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientToolMcpMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="mcp_name" property="mcpName" jdbcType="VARCHAR"/>
        <result column="transport_type" property="transportType" jdbcType="VARCHAR"/>
        <result column="transport_config" property="transportConfig" jdbcType="LONGVARCHAR"/>
        <result column="request_timeout" property="requestTimeout" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, mcp_name, transport_type,
        transport_config, request_timeout, create_time, update_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_tool_mcp (mcp_name, transport_type,
        transport_config, request_timeout, create_time, update_time)
        VALUES (#{mcpName}, #{transportType}, #{transportConfig}, #{requestTimeout}, NOW(), NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_tool_mcp
        WHERE id = #{id}
    </select>

    <select id="selectByMcpName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_tool_mcp
        WHERE mcp_name = #{mcpName}
    </select>

    <select id="selectByTransportType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_tool_mcp
        WHERE transport_type = #{transportType}
        ORDER BY id DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_tool_mcp
        ORDER BY id DESC
    </select>

    <update id="updateById" parameterType="cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO">
        UPDATE ai_client_tool_mcp
        SET mcp_name         = #{mcpName},
            transport_type   = #{transportType},
            transport_config = #{transportConfig},
            request_timeout  = #{requestTimeout},
            update_time      = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_tool_mcp WHERE id = #{id}
    </delete>

</mapper>
