server:
  port: 8091

# 线程池配置
thread:
  pool:
    executor:
      config:
        core-pool-size: 20
        max-pool-size: 50
        keep-alive-time: 5000
        block-queue-size: 5000
        policy: CallerRunsPolicy

# 数据库配置
spring:
  datasource:
    url: *************************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      pool-name: MariaDB_HikariCP
      minimum-idle: 15
      idle-timeout: 180000
      maximum-pool-size: 25
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  # Spring AI 配置
  ai:
    openai:
      api-key: ${OPENAI_API_KEY:your-openai-api-key}
      base-url: ${OPENAI_BASE_URL:https://api.openai.com}
      embedding:
        options:
          model: text-embedding-3-small
          dimensions: 1536
    vectorstore:
      mariadb:
        initialize-schema: true
        distance-type: COSINE
        dimensions: 1536
        table-name: vector_store
        schema-validation: true

# 日志配置用于调试
logging:
  level:
    org.springframework.ai: DEBUG
    org.springframework.beans.factory: DEBUG
    com.zaxxer.hikari: DEBUG
  config: classpath:logback-spring.xml
# MyBatis 配置
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location:  classpath:/mybatis/config/mybatis-config.xml

