# RAG存储服务API文档

## 概述

RAG存储服务已经重构，现在只提供文档上传和删除功能，使用Spring AI ETL Pipeline进行文档处理。

## 主要变更

### 移除的功能
- 知识库创建/删除
- 向量搜索
- 知识库统计信息查询

### 保留的功能
- 文档上传（支持多种格式）
- 文档删除

## API接口

### 1. 上传单个文档文件

**接口地址：** `POST /api/rag/documents/upload`

**请求参数：**
- `ragName` (String): 知识库名称，用于文档标识
- `file` (MultipartFile): 上传的文档文件

**支持的文件格式：**
- PDF (.pdf)
- Word文档 (.doc, .docx)
- PowerPoint (.ppt, .pptx)
- HTML (.html, .htm)

**响应示例：**
```json
{
  "success": true,
  "message": "文档上传成功",
  "ragName": "my-knowledge-base",
  "filename": "document.pdf"
}
```

### 2. 批量上传文档

**接口地址：** `POST /api/rag/documents/batch-upload`

**请求参数：**
- `ragName` (String): 知识库名称
- 请求体：Document对象列表

**请求体示例：**
```json
[
  {
    "id": "doc1",
    "content": "文档内容1",
    "metadata": {
      "source": "document1.txt",
      "author": "张三"
    }
  },
  {
    "id": "doc2",
    "content": "文档内容2",
    "metadata": {
      "source": "document2.txt",
      "author": "李四"
    }
  }
]
```

**响应示例：**
```json
{
  "success": true,
  "message": "文档批量上传成功",
  "ragName": "my-knowledge-base",
  "documentCount": 2
}
```

### 3. 删除知识库的所有文档

**接口地址：** `DELETE /api/rag/documents/{ragName}`

**路径参数：**
- `ragName` (String): 知识库名称

**响应示例：**
```json
{
  "success": true,
  "message": "知识库文档删除成功",
  "ragName": "my-knowledge-base"
}
```

### 4. 根据文档ID删除指定文档

**接口地址：** `DELETE /api/rag/documents/by-ids`

**请求体：** 文档ID列表
```json
["doc1", "doc2", "doc3"]
```

**响应示例：**
```json
{
  "success": true,
  "message": "文档删除成功",
  "deletedCount": 3
}
```

## Spring AI ETL Pipeline集成

### 文档处理流程

1. **DocumentReader阶段：**
   - PDF文件：使用 `PagePdfDocumentReader`
   - Office文档/HTML：使用 `TikaDocumentReader`

2. **DocumentTransformer阶段：**
   - 使用 `TokenTextSplitter` 进行文本分割
   - 分割参数：
     - defaultChunkSize: 800 tokens
     - minChunkSizeChars: 350 characters
     - minChunkLengthToEmbed: 5 characters
     - maxNumChunks: 10000
     - keepSeparator: true

3. **DocumentWriter阶段：**
   - 写入向量存储（VectorStore）
   - 自动添加元数据：
     - `rag_name`: 知识库名称
     - `type`: "document"
     - `added_at`: 添加时间戳

### 元数据结构

每个文档在存储时会自动添加以下元数据：
```json
{
  "rag_name": "知识库名称",
  "type": "document",
  "added_at": 1703123456789,
  "source": "原始文件名",
  "page_number": 1,
  "end_page_number": 1
}
```

## 技术实现

### 服务层
- `IRagStorageService`: 服务接口
- `RagStorageServiceImpl`: 服务实现，集成Spring AI ETL Pipeline

### 控制器层
- `RagStorageController`: REST API控制器

### 依赖项
- `spring-ai-pdf-document-reader`: PDF文档读取
- `spring-ai-tika-document-reader`: 多格式文档读取
- `spring-ai-mariadb-store`: 向量存储

## 使用示例

### 上传PDF文档
```bash
curl -X POST "http://localhost:8080/api/rag/documents/upload" \
  -F "ragName=my-kb" \
  -F "file=@document.pdf"
```

### 删除知识库文档
```bash
curl -X DELETE "http://localhost:8080/api/rag/documents/my-kb"
```

### 批量上传文档
```bash
curl -X POST "http://localhost:8080/api/rag/documents/batch-upload?ragName=my-kb" \
  -H "Content-Type: application/json" \
  -d '[{"id":"doc1","content":"文档内容","metadata":{"source":"test.txt"}}]'
```

## 注意事项

1. **文件大小限制：** 根据Spring Boot配置的文件上传大小限制
2. **支持格式：** 目前支持PDF、DOC/DOCX、PPT/PPTX、HTML格式
3. **文本分割：** 自动进行文本分割，适合向量存储
4. **元数据：** 自动添加知识库标识和时间戳
5. **错误处理：** 所有接口都包含完整的错误处理和日志记录

## 迁移指南

如果您之前使用的是旧版本的RAG API，请注意以下变更：

1. **知识库管理：** 不再需要显式创建知识库，直接上传文档即可
2. **搜索功能：** 已移除，请使用其他方式实现搜索功能
3. **接口路径：** 部分接口路径已更改，请参考上述API文档
4. **文档处理：** 现在使用Spring AI ETL Pipeline，处理更加标准化和高效
