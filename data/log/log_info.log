25-08-04.20:49:04.988 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 20512 (D:\code\ai-agent\ai-agent-app\target\classes started by 17813 in D:\code\ai-agent)
25-08-04.20:49:04.990 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-04.20:49:05.097 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
25-08-04.20:49:05.125 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
25-08-04.20:49:06.753 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.config.DataSourceConfig#MapperScannerRegistrar#0'
25-08-04.20:49:06.756 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.Application#MapperScannerRegistrar#0'
25-08-04.20:49:06.757 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:49:06.781 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-04.20:49:06.900 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
25-08-04.20:49:06.938 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
25-08-04.20:49:06.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
25-08-04.20:49:06.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'forceAutoProxyCreatorToUseClassProxying'
25-08-04.20:49:06.940 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
25-08-04.20:49:06.940 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
25-08-04.20:49:06.941 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
25-08-04.20:49:06.944 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
25-08-04.20:49:06.948 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
25-08-04.20:49:06.948 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
25-08-04.20:49:06.951 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAsyncAnnotationProcessor'
25-08-04.20:49:06.951 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.ProxyAsyncConfiguration'
25-08-04.20:49:06.970 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
25-08-04.20:49:06.971 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
25-08-04.20:49:06.974 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
25-08-04.20:49:06.979 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
25-08-04.20:49:06.979 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
25-08-04.20:49:06.982 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
25-08-04.20:49:06.982 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
25-08-04.20:49:06.983 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
25-08-04.20:49:06.983 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
25-08-04.20:49:06.991 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
25-08-04.20:49:07.001 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
25-08-04.20:49:07.002 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:49:07.146 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:49:07.146 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
25-08-04.20:49:07.194 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
25-08-04.20:49:07.194 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
25-08-04.20:49:07.195 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
25-08-04.20:49:07.195 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
25-08-04.20:49:07.196 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.204 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
25-08-04.20:49:07.212 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.213 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslBundleRegistry'
25-08-04.20:49:07.213 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
25-08-04.20:49:07.215 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:49:07.217 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:07.217 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:49:07.219 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
25-08-04.20:49:07.219 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'fileWatcher'
25-08-04.20:49:07.220 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
25-08-04.20:49:07.226 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
25-08-04.20:49:07.226 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.227 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
25-08-04.20:49:07.227 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
25-08-04.20:49:07.227 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
25-08-04.20:49:07.227 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.229 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
25-08-04.20:49:07.229 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
25-08-04.20:49:07.229 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.265 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
25-08-04.20:49:07.265 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
25-08-04.20:49:07.265 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:49:07.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
25-08-04.20:49:07.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
25-08-04.20:49:07.267 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
25-08-04.20:49:07.267 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
25-08-04.20:49:07.267 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:49:07.272 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:49:07.289 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
25-08-04.20:49:07.289 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:49:07.291 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
25-08-04.20:49:07.291 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
25-08-04.20:49:07.292 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:49:07.297 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:49:07.302 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
25-08-04.20:49:07.433 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-04.20:49:07.450 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-04.20:49:07.451 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-04.20:49:07.452 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-04.20:49:07.552 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-04.20:49:07.553 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2487 ms
25-08-04.20:49:07.557 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
25-08-04.20:49:07.560 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
25-08-04.20:49:07.560 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
25-08-04.20:49:07.563 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
25-08-04.20:49:07.623 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'application'
25-08-04.20:49:07.624 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientInitializer'
25-08-04.20:49:07.629 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultArmoryStrategyFactory'
25-08-04.20:49:07.641 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'rootNode'
25-08-04.20:49:07.644 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolExecutor'
25-08-04.20:49:07.644 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolConfig'
25-08-04.20:49:07.645 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:49:07.648 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolExecutor' via factory method to bean named 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:49:07.651 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientRepositoryImpl'
25-08-04.20:49:07.657 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMapper'
25-08-04.20:49:07.661 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionFactory'
25-08-04.20:49:07.661 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
25-08-04.20:49:07.667 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
25-08-04.20:49:07.667 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:49:07.672 [main            ] DEBUG HikariConfig           - Driver class org.mariadb.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@63947c6b
25-08-04.20:49:07.678 [main            ] DEBUG HikariConfig           - MariaDB_HikariCP - configuration:
25-08-04.20:49:07.684 [main            ] DEBUG HikariConfig           - allowPoolSuspension.............false
25-08-04.20:49:07.684 [main            ] DEBUG HikariConfig           - autoCommit......................true
25-08-04.20:49:07.684 [main            ] DEBUG HikariConfig           - catalog.........................none
25-08-04.20:49:07.684 [main            ] DEBUG HikariConfig           - connectionInitSql...............none
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - connectionTestQuery............."SELECT 1"
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - connectionTimeout...............30000
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - dataSource......................none
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - dataSourceClassName.............none
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - dataSourceJNDI..................none
25-08-04.20:49:07.685 [main            ] DEBUG HikariConfig           - dataSourceProperties............{password=<masked>}
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - driverClassName................."org.mariadb.jdbc.Driver"
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - exceptionOverrideClassName......none
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - healthCheckProperties...........{}
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - healthCheckRegistry.............none
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - idleTimeout.....................180000
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - initializationFailTimeout.......1
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - isolateInternalQueries..........false
25-08-04.20:49:07.686 [main            ] DEBUG HikariConfig           - jdbcUrl.........................jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - keepaliveTime...................0
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - leakDetectionThreshold..........0
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - maxLifetime.....................1800000
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - maximumPoolSize.................25
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - metricRegistry..................none
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - metricsTrackerFactory...........none
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - minimumIdle.....................15
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - password........................<masked>
25-08-04.20:49:07.687 [main            ] DEBUG HikariConfig           - poolName........................"MariaDB_HikariCP"
25-08-04.20:49:07.688 [main            ] DEBUG HikariConfig           - readOnly........................false
25-08-04.20:49:07.688 [main            ] DEBUG HikariConfig           - registerMbeans..................false
25-08-04.20:49:07.688 [main            ] DEBUG HikariConfig           - scheduledExecutor...............none
25-08-04.20:49:07.688 [main            ] DEBUG HikariConfig           - schema..........................none
25-08-04.20:49:07.688 [main            ] DEBUG HikariConfig           - threadFactory...................internal
25-08-04.20:49:07.689 [main            ] DEBUG HikariConfig           - transactionIsolation............default
25-08-04.20:49:07.689 [main            ] DEBUG HikariConfig           - username........................"root"
25-08-04.20:49:07.689 [main            ] DEBUG HikariConfig           - validationTimeout...............5000
25-08-04.20:49:07.692 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-04.20:49:08.022 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@1fc0d9b4
25-08-04.20:49:08.024 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-04.20:49:08.024 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:49:08.026 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
25-08-04.20:49:08.138 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:49:08.138 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:49:08.250 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5da12a93
25-08-04.20:49:08.293 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=2, active=0, idle=2, waiting=0)
25-08-04.20:49:08.343 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
25-08-04.20:49:08.343 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
25-08-04.20:49:08.345 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:49:08.347 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'dataSource'
25-08-04.20:49:08.347 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:49:08.351 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionTemplate'
25-08-04.20:49:08.351 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
25-08-04.20:49:08.352 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:49:08.355 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:49:08.355 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:08.358 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
25-08-04.20:49:08.367 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRelMapper'
25-08-04.20:49:08.368 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMcpRelMapper'
25-08-04.20:49:08.395 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptRepositoryImpl'
25-08-04.20:49:08.397 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptMapper'
25-08-04.20:49:08.404 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelRepositoryImpl'
25-08-04.20:49:08.406 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelMapper'
25-08-04.20:49:08.408 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelToolConfigMapper'
25-08-04.20:49:08.416 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRepositoryImpl'
25-08-04.20:49:08.417 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorMapper'
25-08-04.20:49:08.422 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@34f05bd6
25-08-04.20:49:08.424 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpRepositoryImpl'
25-08-04.20:49:08.426 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpMapper'
25-08-04.20:49:08.433 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpNode'
25-08-04.20:49:08.434 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorNode'
25-08-04.20:49:08.435 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'vectorStore'
25-08-04.20:49:08.435 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcTemplate'
25-08-04.20:49:08.435 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'dataSource'
25-08-04.20:49:08.446 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiEmbeddingModel'
25-08-04.20:49:08.447 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration'
25-08-04.20:49:08.447 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:08.449 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:49:08.456 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'retryTemplate'
25-08-04.20:49:08.456 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration'
25-08-04.20:49:08.456 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:49:08.457 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'retryTemplate' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:49:08.467 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'responseErrorHandler'
25-08-04.20:49:08.467 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=3, active=0, idle=3, waiting=0)
25-08-04.20:49:08.467 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'responseErrorHandler' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:49:08.469 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:08.469 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:49:08.469 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:08.469 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:08.470 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
25-08-04.20:49:08.471 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientBuilderConfigurer'
25-08-04.20:49:08.472 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
25-08-04.20:49:08.472 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
25-08-04.20:49:08.472 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:49:08.473 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:49:08.482 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
25-08-04.20:49:08.482 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:49:08.483 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
25-08-04.20:49:08.484 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'messageConverters'
25-08-04.20:49:08.484 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
25-08-04.20:49:08.487 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stringHttpMessageConverter'
25-08-04.20:49:08.487 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
25-08-04.20:49:08.488 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
25-08-04.20:49:08.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
25-08-04.20:49:08.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
25-08-04.20:49:08.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonObjectMapper'
25-08-04.20:49:08.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
25-08-04.20:49:08.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
25-08-04.20:49:08.493 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:49:08.493 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
25-08-04.20:49:08.494 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:49:08.496 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:49:08.498 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'parameterNamesModule'
25-08-04.20:49:08.498 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
25-08-04.20:49:08.501 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModule'
25-08-04.20:49:08.501 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
25-08-04.20:49:08.502 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModuleEntries'
25-08-04.20:49:08.502 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:08.502 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
25-08-04.20:49:08.526 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:08.526 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
25-08-04.20:49:08.527 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonComponentModule'
25-08-04.20:49:08.527 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
25-08-04.20:49:08.530 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:08.530 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:49:08.533 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
25-08-04.20:49:08.544 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:49:08.553 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientBuilder' via factory method to bean named 'restClientBuilderConfigurer'
25-08-04.20:49:08.572 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@4281863
25-08-04.20:49:08.606 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=4, active=0, idle=4, waiting=0)
25-08-04.20:49:08.767 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@6d3b4703
25-08-04.20:49:08.796 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration'
25-08-04.20:49:08.800 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnectorCustomizer'
25-08-04.20:49:08.800 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration'
25-08-04.20:49:08.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnector'
25-08-04.20:49:08.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdkClientHttpConnectorFactory'
25-08-04.20:49:08.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorFactoryConfiguration$JdkClient'
25-08-04.20:49:08.804 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnector' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:49:08.809 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=5, active=0, idle=5, waiting=0)
25-08-04.20:49:08.810 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnectorCustomizer' via factory method to bean named 'webClientHttpConnector'
25-08-04.20:49:08.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'exchangeStrategiesCustomizer'
25-08-04.20:49:08.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration$WebClientCodecsConfiguration'
25-08-04.20:49:08.812 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultCodecCustomizer'
25-08-04.20:49:08.812 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration'
25-08-04.20:49:08.813 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:49:08.814 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'defaultCodecCustomizer' via factory method to bean named 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:49:08.815 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
25-08-04.20:49:08.815 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
25-08-04.20:49:08.815 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:49:08.966 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'jdbcTemplate'
25-08-04.20:49:08.966 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'openAiEmbeddingModel'
25-08-04.20:49:08.981 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5a3fb25f
25-08-04.20:49:09.025 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=6, active=0, idle=6, waiting=0)
25-08-04.20:49:09.111 [main            ] INFO  MariaDBVectorStore     - Using the vector table name: vector_store. Is empty: false
25-08-04.20:49:09.115 [main            ] INFO  MariaDBVectorStore     - Initializing MariaDBVectorStore schema for table: vector_store in schema: ai_agent_db
25-08-04.20:49:09.115 [main            ] INFO  MariaDBVectorStore     - vectorTableValidationsEnabled false
25-08-04.20:49:09.143 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5bb17a58
25-08-04.20:49:09.180 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=7, active=1, idle=6, waiting=0)
25-08-04.20:49:09.239 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelNode'
25-08-04.20:49:09.241 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientNode'
25-08-04.20:49:09.250 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'guavaConfig'
25-08-04.20:49:09.250 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientController'
25-08-04.20:49:09.254 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageController'
25-08-04.20:49:09.257 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageServiceImpl'
25-08-04.20:49:09.259 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionManager'
25-08-04.20:49:09.259 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'dataSource'
25-08-04.20:49:09.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
25-08-04.20:49:09.268 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cache'
25-08-04.20:49:09.311 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@1eeeb0ca
25-08-04.20:49:09.312 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
25-08-04.20:49:09.312 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
25-08-04.20:49:09.313 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
25-08-04.20:49:09.313 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
25-08-04.20:49:09.314 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
25-08-04.20:49:09.314 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
25-08-04.20:49:09.315 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:49:09.317 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:49:09.320 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
25-08-04.20:49:09.320 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:49:09.321 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
25-08-04.20:49:09.323 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
25-08-04.20:49:09.323 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
25-08-04.20:49:09.325 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'error'
25-08-04.20:49:09.326 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameViewResolver'
25-08-04.20:49:09.328 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
25-08-04.20:49:09.329 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:49:09.332 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:09.332 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:49:09.332 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'conventionErrorViewResolver'
25-08-04.20:49:09.334 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorAttributes'
25-08-04.20:49:09.337 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'basicErrorController'
25-08-04.20:49:09.338 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
25-08-04.20:49:09.344 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
25-08-04.20:49:09.345 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:49:09.345 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:49:09.345 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@201aa8c1'
25-08-04.20:49:09.348 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
25-08-04.20:49:09.349 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:49:09.349 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:49:09.349 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@201aa8c1'
25-08-04.20:49:09.353 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
25-08-04.20:49:09.354 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=8, active=0, idle=8, waiting=0)
25-08-04.20:49:09.354 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcConversionService'
25-08-04.20:49:09.358 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
25-08-04.20:49:09.361 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:09.361 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.361 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.380 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
25-08-04.20:49:09.380 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:09.380 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.380 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.386 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeResolver'
25-08-04.20:49:09.388 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'themeResolver'
25-08-04.20:49:09.390 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'flashMapManager'
25-08-04.20:49:09.392 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewNameTranslator'
25-08-04.20:49:09.393 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcValidator'
25-08-04.20:49:09.401 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-04.20:49:09.402 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
25-08-04.20:49:09.407 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
25-08-04.20:49:09.408 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:49:09.408 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.408 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.467 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPatternParser'
25-08-04.20:49:09.468 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUrlPathHelper'
25-08-04.20:49:09.468 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPathMatcher'
25-08-04.20:49:09.469 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
25-08-04.20:49:09.470 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.470 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.470 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameHandlerMapping'
25-08-04.20:49:09.470 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.470 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.474 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'routerFunctionMapping'
25-08-04.20:49:09.475 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.475 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.480 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'resourceHandlerMapping'
25-08-04.20:49:09.481 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:49:09.481 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.481 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:49:09.482 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@12b84346
25-08-04.20:49:09.499 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
25-08-04.20:49:09.500 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
25-08-04.20:49:09.500 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:49:09.500 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.500 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
25-08-04.20:49:09.526 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=9, active=0, idle=9, waiting=0)
25-08-04.20:49:09.531 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcViewResolver'
25-08-04.20:49:09.533 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:49:09.535 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultViewResolver'
25-08-04.20:49:09.541 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:49:09.541 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@201aa8c1'
25-08-04.20:49:09.543 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:49:09.563 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerFunctionAdapter'
25-08-04.20:49:09.564 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
25-08-04.20:49:09.564 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
25-08-04.20:49:09.564 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
25-08-04.20:49:09.565 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
25-08-04.20:49:09.565 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
25-08-04.20:49:09.565 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerExceptionResolver'
25-08-04.20:49:09.566 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:49:09.580 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
25-08-04.20:49:09.580 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
25-08-04.20:49:09.581 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
25-08-04.20:49:09.581 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
25-08-04.20:49:09.582 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
25-08-04.20:49:09.582 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
25-08-04.20:49:09.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.SseWebFluxTransportAutoConfiguration'
25-08-04.20:49:09.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webFluxClientTransports'
25-08-04.20:49:09.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:49:09.593 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webFluxClientTransports' via factory method to bean named 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:49:09.595 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:49:09.597 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.StdioTransportAutoConfiguration'
25-08-04.20:49:09.597 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stdioTransports'
25-08-04.20:49:09.597 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:49:09.599 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stdioTransports' via factory method to bean named 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:49:09.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpClientAutoConfiguration'
25-08-04.20:49:09.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClients'
25-08-04.20:49:09.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClientConfigurer'
25-08-04.20:49:09.602 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'mcpSyncClientConfigurer'
25-08-04.20:49:09.603 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:49:09.604 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'makeSyncClientsClosable'
25-08-04.20:49:09.604 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'makeSyncClientsClosable' via factory method to bean named 'mcpSyncClients'
25-08-04.20:49:09.605 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration'
25-08-04.20:49:09.605 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpToolCallbacks'
25-08-04.20:49:09.609 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:49:09.610 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration'
25-08-04.20:49:09.611 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatClientBuilderConfigurer'
25-08-04.20:49:09.612 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.client-org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderProperties'
25-08-04.20:49:09.613 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.memory.autoconfigure.ChatMemoryAutoConfiguration'
25-08-04.20:49:09.614 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemoryRepository'
25-08-04.20:49:09.615 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemory'
25-08-04.20:49:09.615 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'chatMemory' via factory method to bean named 'chatMemoryRepository'
25-08-04.20:49:09.617 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:49:09.619 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration'
25-08-04.20:49:09.621 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.observations-org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationProperties'
25-08-04.20:49:09.628 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.embedding.observation.autoconfigure.EmbeddingObservationAutoConfiguration'
25-08-04.20:49:09.629 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:49:09.630 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration'
25-08-04.20:49:09.630 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.image.observations-org.springframework.ai.model.image.observation.autoconfigure.ImageObservationProperties'
25-08-04.20:49:09.631 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration'
25-08-04.20:49:09.631 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gsonBuilder'
25-08-04.20:49:09.631 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardGsonBuilderCustomizer'
25-08-04.20:49:09.631 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:49:09.636 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardGsonBuilderCustomizer' via factory method to bean named 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:49:09.636 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gsonBuilder' via factory method to bean named 'standardGsonBuilderCustomizer'
25-08-04.20:49:09.646 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3d16e5ca
25-08-04.20:49:09.654 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gson'
25-08-04.20:49:09.654 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gson' via factory method to bean named 'gsonBuilder'
25-08-04.20:49:09.679 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
25-08-04.20:49:09.680 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration'
25-08-04.20:49:09.680 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientSsl'
25-08-04.20:49:09.681 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:49:09.681 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=10, active=0, idle=10, waiting=0)
25-08-04.20:49:09.682 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientSsl'
25-08-04.20:49:09.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:49:09.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:49:09.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration'
25-08-04.20:49:09.684 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioSpeechModel'
25-08-04.20:49:09.684 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:49:09.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:09.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:49:09.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:09.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:09.698 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration'
25-08-04.20:49:09.698 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioTranscriptionModel'
25-08-04.20:49:09.698 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:49:09.701 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:09.701 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:49:09.701 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:09.701 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:09.708 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration'
25-08-04.20:49:09.709 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallbackResolver'
25-08-04.20:49:09.711 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5bb3d42d'
25-08-04.20:49:09.711 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'mcpToolCallbacks'
25-08-04.20:49:09.717 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolExecutionExceptionProcessor'
25-08-04.20:49:09.719 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallingManager'
25-08-04.20:49:09.719 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolCallbackResolver'
25-08-04.20:49:09.719 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolExecutionExceptionProcessor'
25-08-04.20:49:09.725 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.tools-org.springframework.ai.model.tool.autoconfigure.ToolCallingProperties'
25-08-04.20:49:09.726 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiChatAutoConfiguration'
25-08-04.20:49:09.727 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiChatModel'
25-08-04.20:49:09.728 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:49:09.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:09.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:49:09.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'toolCallingManager'
25-08-04.20:49:09.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:09.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:09.748 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration'
25-08-04.20:49:09.748 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiImageModel'
25-08-04.20:49:09.749 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:49:09.751 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:09.751 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:49:09.751 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:09.751 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:09.759 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiModerationAutoConfiguration'
25-08-04.20:49:09.760 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiModerationModel'
25-08-04.20:49:09.760 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:49:09.762 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:49:09.762 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:49:09.762 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'retryTemplate'
25-08-04.20:49:09.762 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:49:09.768 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
25-08-04.20:49:09.768 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:49:09.770 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration' via constructor to bean named 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:49:09.770 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanExporter'
25-08-04.20:49:09.770 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectNamingStrategy'
25-08-04.20:49:09.773 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
25-08-04.20:49:09.773 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@201aa8c1'
25-08-04.20:49:09.781 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanServer'
25-08-04.20:49:09.787 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
25-08-04.20:49:09.787 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
25-08-04.20:49:09.787 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'springApplicationAdminRegistrar' via factory method to bean named 'environment'
25-08-04.20:49:09.789 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration'
25-08-04.20:49:09.790 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
25-08-04.20:49:09.790 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
25-08-04.20:49:09.790 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'applicationAvailability'
25-08-04.20:49:09.792 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
25-08-04.20:49:09.792 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
25-08-04.20:49:09.793 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionExecutionListeners'
25-08-04.20:49:09.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
25-08-04.20:49:09.796 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
25-08-04.20:49:09.796 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
25-08-04.20:49:09.798 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleProcessor'
25-08-04.20:49:09.798 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:49:09.799 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:49:09.800 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@7c255cd4
25-08-04.20:49:09.800 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
25-08-04.20:49:09.800 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
25-08-04.20:49:09.802 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:49:09.803 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:49:09.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration'
25-08-04.20:49:09.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
25-08-04.20:49:09.803 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
25-08-04.20:49:09.806 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
25-08-04.20:49:09.807 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
25-08-04.20:49:09.807 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
25-08-04.20:49:09.807 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcClient'
25-08-04.20:49:09.808 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'namedParameterJdbcTemplate'
25-08-04.20:49:09.809 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration'
25-08-04.20:49:09.809 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:49:09.810 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration' via constructor to bean named 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:49:09.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
25-08-04.20:49:09.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
25-08-04.20:49:09.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
25-08-04.20:49:09.811 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:49:09.814 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:49:09.816 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
25-08-04.20:49:09.817 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:49:09.817 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
25-08-04.20:49:09.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
25-08-04.20:49:09.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
25-08-04.20:49:09.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
25-08-04.20:49:09.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
25-08-04.20:49:09.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
25-08-04.20:49:09.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
25-08-04.20:49:09.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionTemplate'
25-08-04.20:49:09.821 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManager'
25-08-04.20:49:09.821 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
25-08-04.20:49:09.823 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
25-08-04.20:49:09.823 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
25-08-04.20:49:09.823 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartResolver'
25-08-04.20:49:09.838 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=11, active=0, idle=11, waiting=0)
25-08-04.20:49:09.843 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-04.20:49:09.856 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-04.20:49:09.868 [main            ] INFO  Application            - Started Application in 5.679 seconds (process running for 8.588)
25-08-04.20:49:09.873 [main            ] INFO  AiClientInitializer    - 开始自动初始化AI客户端...
25-08-04.20:49:09.873 [main            ] INFO  DefaultArmoryStrategyFactory - 开始执行AI客户端组装流程
25-08-04.20:49:09.873 [main            ] INFO  DefaultArmoryStrategyFactory - === 第1阶段：数据加载 ===
25-08-04.20:49:09.961 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@784e4b
25-08-04.20:49:09.991 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=12, active=2, idle=10, waiting=0)
25-08-04.20:49:10.116 [main            ] INFO  RootNode               - 数据加载完成: clients=2, prompts=3, models=2, advisors=2, mcpTools=2
25-08-04.20:49:10.116 [main            ] INFO  DefaultArmoryStrategyFactory - === 第2阶段：MCP工具组装 ===
25-08-04.20:49:10.116 [main            ] INFO  AiClientToolMcpNode    - 开始构建MCP工具客户端，数量: 2
25-08-04.20:49:10.124 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@26e72945
25-08-04.20:49:10.162 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=13, active=0, idle=13, waiting=0)
25-08-04.20:49:10.268 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@46894c6c
25-08-04.20:49:10.300 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=14, active=0, idle=14, waiting=0)
25-08-04.20:49:10.414 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@1ba39250
25-08-04.20:49:10.458 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:49:11.169 [HttpClient-8-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-04.20:49:11.387 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-04.20:49:11.406 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_1
25-08-04.20:49:11.406 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_1 -> weather-tool
25-08-04.20:49:11.925 [HttpClient-9-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=TencentMapWebService, version=1.0.0] and Instructions null
25-08-04.20:49:12.059 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], serverInfo=Implementation[name=TencentMapWebService, version=1.0.0], instructions=null]
25-08-04.20:49:12.059 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_2
25-08-04.20:49:12.060 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_2 -> local-tool
25-08-04.20:49:12.060 [main            ] INFO  AiClientToolMcpNode    - MCP工具客户端构建完成
25-08-04.20:49:12.060 [main            ] INFO  DefaultArmoryStrategyFactory - === 第3阶段：顾问组装 ===
25-08-04.20:49:12.060 [main            ] INFO  AiClientAdvisorNode    - 开始构建顾问，数量: 2
25-08-04.20:49:12.100 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_1
25-08-04.20:49:12.100 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_1 -> 记忆管理顾问 (ChatMemory)
25-08-04.20:49:12.181 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_2
25-08-04.20:49:12.181 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_2 -> RAG问答顾问 (RagAnswer)
25-08-04.20:49:12.181 [main            ] INFO  AiClientAdvisorNode    - 顾问构建完成
25-08-04.20:49:18.105 [main            ] INFO  DefaultArmoryStrategyFactory - === 第4阶段：模型组装 ===
25-08-04.20:49:18.105 [main            ] INFO  AiClientModelNode      - 开始构建模型客户端，数量: 2
25-08-04.20:49:18.106 [main            ] ERROR AiClientModelNode      - 构建模型客户端失败: modelId=1, modelName=qwen-plus, modelType=openai
java.lang.IllegalArgumentException: embeddingsPath cannot be null or empty
	at org.springframework.util.Assert.hasText(Assert.java:253)
	at org.springframework.ai.openai.api.OpenAiApi$Builder.embeddingsPath(OpenAiApi.java:1888)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.createOpenAiChatModel(AiClientModelNode.java:58)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.doApply(AiClientModelNode.java:28)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:55)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
25-08-04.20:49:18.109 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 构建模型客户端失败: qwen-plus
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.doApply(AiClientModelNode.java:35)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:55)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: embeddingsPath cannot be null or empty
	at org.springframework.util.Assert.hasText(Assert.java:253)
	at org.springframework.ai.openai.api.OpenAiApi$Builder.embeddingsPath(OpenAiApi.java:1888)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.createOpenAiChatModel(AiClientModelNode.java:58)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.doApply(AiClientModelNode.java:28)
	... 22 common frames omitted
25-08-04.20:49:18.109 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 构建模型客户端失败: qwen-plus
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.doApply(AiClientModelNode.java:35)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:55)
	... 21 common frames omitted
Caused by: java.lang.IllegalArgumentException: embeddingsPath cannot be null or empty
	at org.springframework.util.Assert.hasText(Assert.java:253)
	at org.springframework.ai.openai.api.OpenAiApi$Builder.embeddingsPath(OpenAiApi.java:1888)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.createOpenAiChatModel(AiClientModelNode.java:58)
	at cn.iflytek.domain.agent.service.node.AiClientModelNode.doApply(AiClientModelNode.java:28)
	... 22 common frames omitted
25-08-04.20:49:18.134 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@380e9789]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-04.20:49:18.141 [main            ] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-04.20:49:18.142 [tomcat-shutdown ] INFO  Http11NioProtocol      - Pausing ProtocolHandler ["http-nio-8091"]
25-08-04.20:49:18.540 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-04.20:49:18.541 [main            ] INFO  Http11NioProtocol      - Stopping ProtocolHandler ["http-nio-8091"]
25-08-04.20:49:18.545 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-04.20:49:18.545 [main            ] DEBUG HikariPool             - MariaDB_HikariCP - Before shutdown stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:49:18.546 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@1fc0d9b4: (connection evicted)
25-08-04.20:49:18.553 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5da12a93: (connection evicted)
25-08-04.20:49:18.569 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@34f05bd6: (connection evicted)
25-08-04.20:49:18.584 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@4281863: (connection evicted)
25-08-04.20:49:18.600 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@6d3b4703: (connection evicted)
25-08-04.20:49:18.616 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5a3fb25f: (connection evicted)
25-08-04.20:49:18.631 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5bb17a58: (connection evicted)
25-08-04.20:49:18.646 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@1eeeb0ca: (connection evicted)
25-08-04.20:49:18.662 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@12b84346: (connection evicted)
25-08-04.20:49:18.678 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3d16e5ca: (connection evicted)
25-08-04.20:49:18.693 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@7c255cd4: (connection evicted)
25-08-04.20:49:18.709 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@784e4b: (connection evicted)
25-08-04.20:49:18.724 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@26e72945: (connection evicted)
25-08-04.20:49:18.739 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@46894c6c: (connection evicted)
25-08-04.20:49:18.754 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@1ba39250: (connection evicted)
25-08-04.20:49:18.770 [main            ] DEBUG HikariPool             - MariaDB_HikariCP - After shutdown stats (total=0, active=0, idle=0, waiting=0)
25-08-04.20:49:18.770 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-04.20:49:18.771 [main            ] DEBUG DisposableBeanAdapter  - Custom destroy method 'shutdown' on bean with name 'threadPoolExecutor' completed
25-08-04.20:50:03.922 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 6624 (D:\code\ai-agent\ai-agent-app\target\classes started by 17813 in D:\code\ai-agent)
25-08-04.20:50:03.925 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-04.20:50:04.011 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
25-08-04.20:50:04.043 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
25-08-04.20:50:04.861 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.config.DataSourceConfig#MapperScannerRegistrar#0'
25-08-04.20:50:04.864 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.Application#MapperScannerRegistrar#0'
25-08-04.20:50:04.865 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
25-08-04.20:50:04.889 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.889 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.889 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.889 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.890 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.890 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.890 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.890 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:50:04.890 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-04.20:50:04.987 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
25-08-04.20:50:05.026 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
25-08-04.20:50:05.027 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
25-08-04.20:50:05.027 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'forceAutoProxyCreatorToUseClassProxying'
25-08-04.20:50:05.028 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
25-08-04.20:50:05.028 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
25-08-04.20:50:05.030 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
25-08-04.20:50:05.032 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
25-08-04.20:50:05.037 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
25-08-04.20:50:05.037 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
25-08-04.20:50:05.039 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAsyncAnnotationProcessor'
25-08-04.20:50:05.039 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.ProxyAsyncConfiguration'
25-08-04.20:50:05.056 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
25-08-04.20:50:05.057 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
25-08-04.20:50:05.059 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
25-08-04.20:50:05.064 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
25-08-04.20:50:05.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
25-08-04.20:50:05.068 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
25-08-04.20:50:05.068 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
25-08-04.20:50:05.069 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
25-08-04.20:50:05.069 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
25-08-04.20:50:05.074 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
25-08-04.20:50:05.080 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
25-08-04.20:50:05.080 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:50:05.185 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:50:05.185 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
25-08-04.20:50:05.231 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
25-08-04.20:50:05.231 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
25-08-04.20:50:05.233 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
25-08-04.20:50:05.233 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
25-08-04.20:50:05.234 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.242 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
25-08-04.20:50:05.251 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.253 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslBundleRegistry'
25-08-04.20:50:05.253 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
25-08-04.20:50:05.255 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:50:05.257 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:05.257 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:50:05.260 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
25-08-04.20:50:05.260 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'fileWatcher'
25-08-04.20:50:05.261 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
25-08-04.20:50:05.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
25-08-04.20:50:05.267 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.268 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
25-08-04.20:50:05.268 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
25-08-04.20:50:05.269 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
25-08-04.20:50:05.269 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.271 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
25-08-04.20:50:05.271 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
25-08-04.20:50:05.271 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.314 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
25-08-04.20:50:05.314 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
25-08-04.20:50:05.315 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:50:05.338 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
25-08-04.20:50:05.338 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
25-08-04.20:50:05.339 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
25-08-04.20:50:05.340 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
25-08-04.20:50:05.342 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:50:05.348 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:50:05.367 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
25-08-04.20:50:05.367 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:50:05.369 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
25-08-04.20:50:05.369 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
25-08-04.20:50:05.370 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:50:05.374 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:50:05.379 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
25-08-04.20:50:05.549 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-04.20:50:05.568 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-04.20:50:05.570 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-04.20:50:05.571 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-04.20:50:05.699 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-04.20:50:05.700 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1709 ms
25-08-04.20:50:05.705 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
25-08-04.20:50:05.708 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
25-08-04.20:50:05.708 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
25-08-04.20:50:05.711 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
25-08-04.20:50:05.779 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'application'
25-08-04.20:50:05.781 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientInitializer'
25-08-04.20:50:05.795 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultArmoryStrategyFactory'
25-08-04.20:50:05.796 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'rootNode'
25-08-04.20:50:05.798 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolExecutor'
25-08-04.20:50:05.798 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolConfig'
25-08-04.20:50:05.799 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:50:05.804 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolExecutor' via factory method to bean named 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:50:05.806 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientRepositoryImpl'
25-08-04.20:50:05.813 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMapper'
25-08-04.20:50:05.818 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionFactory'
25-08-04.20:50:05.818 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
25-08-04.20:50:05.825 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
25-08-04.20:50:05.825 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:50:05.828 [main            ] DEBUG HikariConfig           - Driver class org.mariadb.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@63947c6b
25-08-04.20:50:05.833 [main            ] DEBUG HikariConfig           - MariaDB_HikariCP - configuration:
25-08-04.20:50:05.840 [main            ] DEBUG HikariConfig           - allowPoolSuspension.............false
25-08-04.20:50:05.840 [main            ] DEBUG HikariConfig           - autoCommit......................true
25-08-04.20:50:05.840 [main            ] DEBUG HikariConfig           - catalog.........................none
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - connectionInitSql...............none
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - connectionTestQuery............."SELECT 1"
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - connectionTimeout...............30000
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - dataSource......................none
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - dataSourceClassName.............none
25-08-04.20:50:05.841 [main            ] DEBUG HikariConfig           - dataSourceJNDI..................none
25-08-04.20:50:05.842 [main            ] DEBUG HikariConfig           - dataSourceProperties............{password=<masked>}
25-08-04.20:50:05.842 [main            ] DEBUG HikariConfig           - driverClassName................."org.mariadb.jdbc.Driver"
25-08-04.20:50:05.842 [main            ] DEBUG HikariConfig           - exceptionOverrideClassName......none
25-08-04.20:50:05.842 [main            ] DEBUG HikariConfig           - healthCheckProperties...........{}
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - healthCheckRegistry.............none
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - idleTimeout.....................180000
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - initializationFailTimeout.......1
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - isolateInternalQueries..........false
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - jdbcUrl.........................jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - keepaliveTime...................0
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - leakDetectionThreshold..........0
25-08-04.20:50:05.843 [main            ] DEBUG HikariConfig           - maxLifetime.....................1800000
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - maximumPoolSize.................25
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - metricRegistry..................none
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - metricsTrackerFactory...........none
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - minimumIdle.....................15
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - password........................<masked>
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - poolName........................"MariaDB_HikariCP"
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - readOnly........................false
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - registerMbeans..................false
25-08-04.20:50:05.844 [main            ] DEBUG HikariConfig           - scheduledExecutor...............none
25-08-04.20:50:05.845 [main            ] DEBUG HikariConfig           - schema..........................none
25-08-04.20:50:05.845 [main            ] DEBUG HikariConfig           - threadFactory...................internal
25-08-04.20:50:05.845 [main            ] DEBUG HikariConfig           - transactionIsolation............default
25-08-04.20:50:05.845 [main            ] DEBUG HikariConfig           - username........................"root"
25-08-04.20:50:05.845 [main            ] DEBUG HikariConfig           - validationTimeout...............5000
25-08-04.20:50:05.847 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-04.20:50:06.210 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@7c2b6acb
25-08-04.20:50:06.219 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-04.20:50:06.220 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:50:06.231 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
25-08-04.20:50:06.321 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:50:06.322 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:50:06.473 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@34bf0c3c
25-08-04.20:50:06.507 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=2, active=0, idle=2, waiting=0)
25-08-04.20:50:06.545 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
25-08-04.20:50:06.545 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
25-08-04.20:50:06.546 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:50:06.548 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'dataSource'
25-08-04.20:50:06.548 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:50:06.552 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionTemplate'
25-08-04.20:50:06.552 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
25-08-04.20:50:06.552 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:50:06.555 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:50:06.555 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:06.558 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
25-08-04.20:50:06.565 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRelMapper'
25-08-04.20:50:06.567 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMcpRelMapper'
25-08-04.20:50:06.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptRepositoryImpl'
25-08-04.20:50:06.592 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptMapper'
25-08-04.20:50:06.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelRepositoryImpl'
25-08-04.20:50:06.601 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelMapper'
25-08-04.20:50:06.603 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelToolConfigMapper'
25-08-04.20:50:06.610 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRepositoryImpl'
25-08-04.20:50:06.611 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorMapper'
25-08-04.20:50:06.617 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpRepositoryImpl'
25-08-04.20:50:06.619 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpMapper'
25-08-04.20:50:06.626 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpNode'
25-08-04.20:50:06.627 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorNode'
25-08-04.20:50:06.627 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'vectorStore'
25-08-04.20:50:06.628 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcTemplate'
25-08-04.20:50:06.628 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'dataSource'
25-08-04.20:50:06.635 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiEmbeddingModel'
25-08-04.20:50:06.635 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration'
25-08-04.20:50:06.636 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:06.637 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:50:06.643 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'retryTemplate'
25-08-04.20:50:06.643 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration'
25-08-04.20:50:06.643 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:50:06.645 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'retryTemplate' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:50:06.651 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@2a3e5c0e
25-08-04.20:50:06.654 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'responseErrorHandler'
25-08-04.20:50:06.654 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'responseErrorHandler' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:50:06.655 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:06.655 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:50:06.655 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:06.655 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:06.656 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
25-08-04.20:50:06.656 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientBuilderConfigurer'
25-08-04.20:50:06.658 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
25-08-04.20:50:06.658 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
25-08-04.20:50:06.658 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:50:06.659 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:50:06.665 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
25-08-04.20:50:06.665 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:50:06.666 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
25-08-04.20:50:06.667 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'messageConverters'
25-08-04.20:50:06.667 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
25-08-04.20:50:06.669 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stringHttpMessageConverter'
25-08-04.20:50:06.669 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
25-08-04.20:50:06.670 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
25-08-04.20:50:06.673 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
25-08-04.20:50:06.673 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
25-08-04.20:50:06.674 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonObjectMapper'
25-08-04.20:50:06.674 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
25-08-04.20:50:06.674 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
25-08-04.20:50:06.675 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:50:06.675 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
25-08-04.20:50:06.675 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:50:06.678 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:50:06.679 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'parameterNamesModule'
25-08-04.20:50:06.679 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
25-08-04.20:50:06.682 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModule'
25-08-04.20:50:06.682 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
25-08-04.20:50:06.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModuleEntries'
25-08-04.20:50:06.683 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:06.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
25-08-04.20:50:06.691 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=3, active=0, idle=3, waiting=0)
25-08-04.20:50:06.706 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:06.706 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
25-08-04.20:50:06.707 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonComponentModule'
25-08-04.20:50:06.707 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
25-08-04.20:50:06.711 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:06.711 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:50:06.714 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
25-08-04.20:50:06.724 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:50:06.733 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientBuilder' via factory method to bean named 'restClientBuilderConfigurer'
25-08-04.20:50:06.800 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@650d284d
25-08-04.20:50:06.831 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=4, active=0, idle=4, waiting=0)
25-08-04.20:50:06.926 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration'
25-08-04.20:50:06.928 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnectorCustomizer'
25-08-04.20:50:06.928 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration'
25-08-04.20:50:06.929 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnector'
25-08-04.20:50:06.930 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdkClientHttpConnectorFactory'
25-08-04.20:50:06.930 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorFactoryConfiguration$JdkClient'
25-08-04.20:50:06.931 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnector' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:50:06.937 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnectorCustomizer' via factory method to bean named 'webClientHttpConnector'
25-08-04.20:50:06.938 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'exchangeStrategiesCustomizer'
25-08-04.20:50:06.938 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration$WebClientCodecsConfiguration'
25-08-04.20:50:06.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultCodecCustomizer'
25-08-04.20:50:06.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration'
25-08-04.20:50:06.940 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:50:06.942 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'defaultCodecCustomizer' via factory method to bean named 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:50:06.943 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@69065639
25-08-04.20:50:06.943 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
25-08-04.20:50:06.943 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
25-08-04.20:50:06.944 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:50:06.988 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=5, active=0, idle=5, waiting=0)
25-08-04.20:50:07.061 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'jdbcTemplate'
25-08-04.20:50:07.061 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'openAiEmbeddingModel'
25-08-04.20:50:07.095 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@241ebeec
25-08-04.20:50:07.131 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=6, active=0, idle=6, waiting=0)
25-08-04.20:50:07.187 [main            ] INFO  MariaDBVectorStore     - Using the vector table name: vector_store. Is empty: false
25-08-04.20:50:07.190 [main            ] INFO  MariaDBVectorStore     - Initializing MariaDBVectorStore schema for table: vector_store in schema: ai_agent_db
25-08-04.20:50:07.190 [main            ] INFO  MariaDBVectorStore     - vectorTableValidationsEnabled false
25-08-04.20:50:07.269 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3aeea2f3
25-08-04.20:50:07.313 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=7, active=1, idle=6, waiting=0)
25-08-04.20:50:07.321 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelNode'
25-08-04.20:50:07.322 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientNode'
25-08-04.20:50:07.329 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'guavaConfig'
25-08-04.20:50:07.330 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientController'
25-08-04.20:50:07.333 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageController'
25-08-04.20:50:07.335 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageServiceImpl'
25-08-04.20:50:07.336 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionManager'
25-08-04.20:50:07.337 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'dataSource'
25-08-04.20:50:07.341 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
25-08-04.20:50:07.344 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cache'
25-08-04.20:50:07.361 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
25-08-04.20:50:07.361 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
25-08-04.20:50:07.362 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
25-08-04.20:50:07.362 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
25-08-04.20:50:07.362 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
25-08-04.20:50:07.362 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
25-08-04.20:50:07.363 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:50:07.365 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:50:07.366 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
25-08-04.20:50:07.367 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:50:07.367 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
25-08-04.20:50:07.368 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
25-08-04.20:50:07.369 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
25-08-04.20:50:07.370 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'error'
25-08-04.20:50:07.370 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameViewResolver'
25-08-04.20:50:07.371 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
25-08-04.20:50:07.371 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:50:07.375 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:07.375 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:50:07.375 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'conventionErrorViewResolver'
25-08-04.20:50:07.377 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorAttributes'
25-08-04.20:50:07.378 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'basicErrorController'
25-08-04.20:50:07.379 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
25-08-04.20:50:07.382 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
25-08-04.20:50:07.383 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:50:07.383 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:50:07.383 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:50:07.385 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
25-08-04.20:50:07.385 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:50:07.386 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:50:07.386 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:50:07.390 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
25-08-04.20:50:07.391 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcConversionService'
25-08-04.20:50:07.407 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
25-08-04.20:50:07.411 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:07.411 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.411 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.426 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
25-08-04.20:50:07.426 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:07.426 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.426 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.430 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeResolver'
25-08-04.20:50:07.431 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'themeResolver'
25-08-04.20:50:07.431 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'flashMapManager'
25-08-04.20:50:07.434 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewNameTranslator'
25-08-04.20:50:07.435 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcValidator'
25-08-04.20:50:07.442 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-04.20:50:07.442 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
25-08-04.20:50:07.448 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
25-08-04.20:50:07.449 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:50:07.449 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.449 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.497 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPatternParser'
25-08-04.20:50:07.498 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@6c19a56c
25-08-04.20:50:07.498 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUrlPathHelper'
25-08-04.20:50:07.498 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPathMatcher'
25-08-04.20:50:07.499 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
25-08-04.20:50:07.499 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.499 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.500 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameHandlerMapping'
25-08-04.20:50:07.500 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.500 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.503 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'routerFunctionMapping'
25-08-04.20:50:07.503 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.503 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.507 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'resourceHandlerMapping'
25-08-04.20:50:07.508 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:50:07.508 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.508 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:50:07.523 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
25-08-04.20:50:07.523 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
25-08-04.20:50:07.523 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:50:07.523 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.523 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
25-08-04.20:50:07.534 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=8, active=0, idle=8, waiting=0)
25-08-04.20:50:07.552 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcViewResolver'
25-08-04.20:50:07.552 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:50:07.554 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultViewResolver'
25-08-04.20:50:07.560 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:50:07.560 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:50:07.561 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:50:07.582 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerFunctionAdapter'
25-08-04.20:50:07.583 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
25-08-04.20:50:07.584 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
25-08-04.20:50:07.584 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
25-08-04.20:50:07.584 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
25-08-04.20:50:07.585 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
25-08-04.20:50:07.585 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerExceptionResolver'
25-08-04.20:50:07.585 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:50:07.590 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
25-08-04.20:50:07.590 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
25-08-04.20:50:07.590 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
25-08-04.20:50:07.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
25-08-04.20:50:07.591 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
25-08-04.20:50:07.592 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
25-08-04.20:50:07.598 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.SseWebFluxTransportAutoConfiguration'
25-08-04.20:50:07.598 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webFluxClientTransports'
25-08-04.20:50:07.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:50:07.599 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webFluxClientTransports' via factory method to bean named 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:50:07.601 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:50:07.603 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.StdioTransportAutoConfiguration'
25-08-04.20:50:07.603 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stdioTransports'
25-08-04.20:50:07.603 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:50:07.604 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stdioTransports' via factory method to bean named 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:50:07.605 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpClientAutoConfiguration'
25-08-04.20:50:07.605 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClients'
25-08-04.20:50:07.605 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClientConfigurer'
25-08-04.20:50:07.607 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'mcpSyncClientConfigurer'
25-08-04.20:50:07.607 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:50:07.608 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'makeSyncClientsClosable'
25-08-04.20:50:07.608 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'makeSyncClientsClosable' via factory method to bean named 'mcpSyncClients'
25-08-04.20:50:07.609 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration'
25-08-04.20:50:07.609 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpToolCallbacks'
25-08-04.20:50:07.611 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:50:07.613 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration'
25-08-04.20:50:07.613 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatClientBuilderConfigurer'
25-08-04.20:50:07.614 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.client-org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderProperties'
25-08-04.20:50:07.615 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.memory.autoconfigure.ChatMemoryAutoConfiguration'
25-08-04.20:50:07.615 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemoryRepository'
25-08-04.20:50:07.617 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemory'
25-08-04.20:50:07.617 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'chatMemory' via factory method to bean named 'chatMemoryRepository'
25-08-04.20:50:07.619 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:50:07.620 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration'
25-08-04.20:50:07.621 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.observations-org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationProperties'
25-08-04.20:50:07.622 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.embedding.observation.autoconfigure.EmbeddingObservationAutoConfiguration'
25-08-04.20:50:07.623 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:50:07.623 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration'
25-08-04.20:50:07.624 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.image.observations-org.springframework.ai.model.image.observation.autoconfigure.ImageObservationProperties'
25-08-04.20:50:07.624 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration'
25-08-04.20:50:07.625 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gsonBuilder'
25-08-04.20:50:07.625 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardGsonBuilderCustomizer'
25-08-04.20:50:07.626 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:50:07.628 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardGsonBuilderCustomizer' via factory method to bean named 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:50:07.629 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gsonBuilder' via factory method to bean named 'standardGsonBuilderCustomizer'
25-08-04.20:50:07.648 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@79433893
25-08-04.20:50:07.650 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gson'
25-08-04.20:50:07.650 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gson' via factory method to bean named 'gsonBuilder'
25-08-04.20:50:07.678 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
25-08-04.20:50:07.680 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration'
25-08-04.20:50:07.680 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientSsl'
25-08-04.20:50:07.681 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:50:07.682 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientSsl'
25-08-04.20:50:07.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:50:07.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:50:07.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration'
25-08-04.20:50:07.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioSpeechModel'
25-08-04.20:50:07.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:50:07.687 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=9, active=0, idle=9, waiting=0)
25-08-04.20:50:07.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:07.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:50:07.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:07.688 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:07.700 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration'
25-08-04.20:50:07.701 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioTranscriptionModel'
25-08-04.20:50:07.701 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:50:07.705 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:07.705 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:50:07.705 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:07.705 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:07.715 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration'
25-08-04.20:50:07.715 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallbackResolver'
25-08-04.20:50:07.717 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:50:07.717 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'mcpToolCallbacks'
25-08-04.20:50:07.729 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolExecutionExceptionProcessor'
25-08-04.20:50:07.731 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallingManager'
25-08-04.20:50:07.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolCallbackResolver'
25-08-04.20:50:07.731 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolExecutionExceptionProcessor'
25-08-04.20:50:07.738 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.tools-org.springframework.ai.model.tool.autoconfigure.ToolCallingProperties'
25-08-04.20:50:07.739 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiChatAutoConfiguration'
25-08-04.20:50:07.740 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiChatModel'
25-08-04.20:50:07.741 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:50:07.745 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:07.745 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:50:07.745 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'toolCallingManager'
25-08-04.20:50:07.745 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:07.745 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:07.762 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration'
25-08-04.20:50:07.762 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiImageModel'
25-08-04.20:50:07.763 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:50:07.764 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:07.764 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:50:07.764 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:07.764 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:07.773 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiModerationAutoConfiguration'
25-08-04.20:50:07.774 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiModerationModel'
25-08-04.20:50:07.774 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:50:07.776 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:50:07.776 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:50:07.776 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'retryTemplate'
25-08-04.20:50:07.776 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:50:07.782 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
25-08-04.20:50:07.783 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:50:07.783 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration' via constructor to bean named 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:50:07.783 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanExporter'
25-08-04.20:50:07.784 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectNamingStrategy'
25-08-04.20:50:07.786 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
25-08-04.20:50:07.786 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:50:07.789 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanServer'
25-08-04.20:50:07.795 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
25-08-04.20:50:07.796 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
25-08-04.20:50:07.796 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'springApplicationAdminRegistrar' via factory method to bean named 'environment'
25-08-04.20:50:07.798 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration'
25-08-04.20:50:07.799 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
25-08-04.20:50:07.799 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
25-08-04.20:50:07.799 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'applicationAvailability'
25-08-04.20:50:07.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
25-08-04.20:50:07.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
25-08-04.20:50:07.802 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionExecutionListeners'
25-08-04.20:50:07.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
25-08-04.20:50:07.805 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@7c660c4e
25-08-04.20:50:07.805 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
25-08-04.20:50:07.805 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
25-08-04.20:50:07.806 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleProcessor'
25-08-04.20:50:07.806 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:50:07.807 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:50:07.808 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
25-08-04.20:50:07.808 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
25-08-04.20:50:07.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:50:07.811 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:50:07.811 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration'
25-08-04.20:50:07.811 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
25-08-04.20:50:07.812 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
25-08-04.20:50:07.815 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
25-08-04.20:50:07.815 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
25-08-04.20:50:07.816 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
25-08-04.20:50:07.816 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcClient'
25-08-04.20:50:07.816 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'namedParameterJdbcTemplate'
25-08-04.20:50:07.817 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration'
25-08-04.20:50:07.818 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:50:07.819 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration' via constructor to bean named 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:50:07.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
25-08-04.20:50:07.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
25-08-04.20:50:07.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
25-08-04.20:50:07.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:50:07.821 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:50:07.822 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
25-08-04.20:50:07.823 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:50:07.823 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
25-08-04.20:50:07.824 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
25-08-04.20:50:07.825 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
25-08-04.20:50:07.825 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
25-08-04.20:50:07.826 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
25-08-04.20:50:07.826 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
25-08-04.20:50:07.826 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
25-08-04.20:50:07.826 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionTemplate'
25-08-04.20:50:07.826 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManager'
25-08-04.20:50:07.828 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
25-08-04.20:50:07.828 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
25-08-04.20:50:07.829 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
25-08-04.20:50:07.829 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartResolver'
25-08-04.20:50:07.842 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=10, active=0, idle=10, waiting=0)
25-08-04.20:50:07.848 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-04.20:50:07.859 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-04.20:50:07.870 [main            ] INFO  Application            - Started Application in 4.848 seconds (process running for 6.204)
25-08-04.20:50:07.873 [main            ] INFO  AiClientInitializer    - 开始自动初始化AI客户端...
25-08-04.20:50:07.873 [main            ] INFO  DefaultArmoryStrategyFactory - 开始执行AI客户端组装流程
25-08-04.20:50:07.874 [main            ] INFO  DefaultArmoryStrategyFactory - === 第1阶段：数据加载 ===
25-08-04.20:50:07.941 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@51eda84c
25-08-04.20:50:07.981 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=11, active=5, idle=6, waiting=0)
25-08-04.20:50:08.155 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@4af4e4ce
25-08-04.20:50:08.162 [main            ] INFO  RootNode               - 数据加载完成: clients=2, prompts=3, models=2, advisors=2, mcpTools=2
25-08-04.20:50:08.163 [main            ] INFO  DefaultArmoryStrategyFactory - === 第2阶段：MCP工具组装 ===
25-08-04.20:50:08.163 [main            ] INFO  AiClientToolMcpNode    - 开始构建MCP工具客户端，数量: 2
25-08-04.20:50:08.200 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=12, active=0, idle=12, waiting=0)
25-08-04.20:50:08.330 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@27c12303
25-08-04.20:50:08.369 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=13, active=0, idle=13, waiting=0)
25-08-04.20:50:08.486 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5fbe56d4
25-08-04.20:50:08.525 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=14, active=0, idle=14, waiting=0)
25-08-04.20:50:08.667 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5e380a9
25-08-04.20:50:08.710 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:50:08.977 [HttpClient-8-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-04.20:50:09.223 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-04.20:50:09.230 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_1
25-08-04.20:50:09.230 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_1 -> weather-tool
25-08-04.20:50:09.654 [HttpClient-9-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=TencentMapWebService, version=1.0.0] and Instructions null
25-08-04.20:50:09.769 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], serverInfo=Implementation[name=TencentMapWebService, version=1.0.0], instructions=null]
25-08-04.20:50:09.769 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_2
25-08-04.20:50:09.769 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_2 -> local-tool
25-08-04.20:50:09.769 [main            ] INFO  AiClientToolMcpNode    - MCP工具客户端构建完成
25-08-04.20:50:09.769 [main            ] INFO  DefaultArmoryStrategyFactory - === 第3阶段：顾问组装 ===
25-08-04.20:50:09.769 [main            ] INFO  AiClientAdvisorNode    - 开始构建顾问，数量: 2
25-08-04.20:50:09.781 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_1
25-08-04.20:50:09.781 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_1 -> 记忆管理顾问 (ChatMemory)
25-08-04.20:50:09.839 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_2
25-08-04.20:50:09.839 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_2 -> RAG问答顾问 (RagAnswer)
25-08-04.20:50:09.839 [main            ] INFO  AiClientAdvisorNode    - 顾问构建完成
25-08-04.20:50:09.839 [main            ] INFO  DefaultArmoryStrategyFactory - === 第4阶段：模型组装 ===
25-08-04.20:50:09.839 [main            ] INFO  AiClientModelNode      - 开始构建模型客户端，数量: 2
25-08-04.20:50:09.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_2'
25-08-04.20:50:09.847 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_1'
25-08-04.20:50:10.172 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_1
25-08-04.20:50:10.172 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_1 -> qwen-plus (openai)
25-08-04.20:50:10.176 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_2
25-08-04.20:50:10.176 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_2 -> ollama (openai)
25-08-04.20:50:10.176 [main            ] INFO  AiClientModelNode      - 模型客户端构建完成
25-08-04.20:50:10.176 [main            ] INFO  DefaultArmoryStrategyFactory - === 第5阶段：AI客户端组装 ===
25-08-04.20:50:10.176 [main            ] INFO  AiClientNode           - 开始构建AI客户端，数量: 2
25-08-04.20:50:10.176 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientModel_2'
25-08-04.20:50:10.177 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientAdvisor_1'
25-08-04.20:51:40.802 [MariaDB_HikariCP housekeeper] WARN  HikariPool             - MariaDB_HikariCP - Thread starvation or clock leap detected (housekeeper delta=1m34s480ms434µs).
25-08-04.20:51:40.802 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:51:40.802 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:51:40.802 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:51:40.810 [HttpClient-8-Worker-2] ERROR HttpClientSseClientTransport - SSE connection error
java.io.IOException: http1_0 content, bytes received: 7464
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:979)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:934)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:934)
25-08-04.20:51:40.812 [ForkJoinPool.commonPool-worker-2] ERROR HttpClientSseClientTransport - SSE connection error
java.util.concurrent.CompletionException: java.io.IOException: http1_0 content, bytes received: 7464
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:332)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:347)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:874)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.ResponseSubscribers.lambda$getBodyAsync$2(ResponseSubscribers.java:1152)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.loop(LineSubscriberAdapter.java:410)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.signalError(LineSubscriberAdapter.java:199)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter.onError(LineSubscriberAdapter.java:105)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.propagateError(Http1Response.java:327)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.complete(Http1Response.java:356)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.onError(Http1Response.java:386)
	at java.net.http/jdk.internal.net.http.Http1Response.lambda$readBody$2(Http1Response.java:468)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.Http1Response.onReadError(Http1Response.java:554)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:761)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.io.IOException: http1_0 content, bytes received: 7464
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	... 8 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:979)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:934)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:934)
25-08-04.20:51:41.083 [ForkJoinPool.commonPool-worker-2] ERROR HttpClientSseClientTransport - Error sending message: 400
25-08-04.20:52:10.803 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:52:10.803 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:52:10.803 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:52:17.436 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-04.20:52:17.981 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-04.20:52:17.985 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-04.20:52:17.985 [SpringApplicationShutdownHook] DEBUG HikariPool             - MariaDB_HikariCP - Before shutdown stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:52:17.986 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@7c2b6acb: (connection evicted)
25-08-04.20:52:18.001 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@34bf0c3c: (connection evicted)
25-08-04.20:52:18.017 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@2a3e5c0e: (connection evicted)
25-08-04.20:52:18.032 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@650d284d: (connection evicted)
25-08-04.20:52:18.048 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@69065639: (connection evicted)
25-08-04.20:52:18.064 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@241ebeec: (connection evicted)
25-08-04.20:52:18.079 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3aeea2f3: (connection evicted)
25-08-04.20:52:18.094 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@6c19a56c: (connection evicted)
25-08-04.20:52:18.110 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@79433893: (connection evicted)
25-08-04.20:52:18.124 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@7c660c4e: (connection evicted)
25-08-04.20:52:18.140 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@51eda84c: (connection evicted)
25-08-04.20:52:18.156 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@4af4e4ce: (connection evicted)
25-08-04.20:52:18.173 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@27c12303: (connection evicted)
25-08-04.20:52:18.188 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5fbe56d4: (connection evicted)
25-08-04.20:52:18.203 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5e380a9: (connection evicted)
25-08-04.20:52:18.218 [SpringApplicationShutdownHook] DEBUG HikariPool             - MariaDB_HikariCP - After shutdown stats (total=0, active=0, idle=0, waiting=0)
25-08-04.20:52:18.218 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-04.20:52:18.218 [SpringApplicationShutdownHook] DEBUG DisposableBeanAdapter  - Custom destroy method 'shutdown' on bean with name 'threadPoolExecutor' completed
25-08-04.20:54:59.732 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 32184 (D:\code\ai-agent\ai-agent-app\target\classes started by 17813 in D:\code\ai-agent)
25-08-04.20:54:59.735 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-04.20:54:59.885 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
25-08-04.20:54:59.935 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
25-08-04.20:55:01.173 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.config.DataSourceConfig#MapperScannerRegistrar#0'
25-08-04.20:55:01.176 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.Application#MapperScannerRegistrar#0'
25-08-04.20:55:01.177 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
25-08-04.20:55:01.205 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.205 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.205 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.205 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.206 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.206 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.206 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.206 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:55:01.206 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-04.20:55:01.322 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
25-08-04.20:55:01.361 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
25-08-04.20:55:01.362 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
25-08-04.20:55:01.363 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'forceAutoProxyCreatorToUseClassProxying'
25-08-04.20:55:01.364 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
25-08-04.20:55:01.364 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
25-08-04.20:55:01.366 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
25-08-04.20:55:01.368 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
25-08-04.20:55:01.373 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
25-08-04.20:55:01.373 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
25-08-04.20:55:01.375 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAsyncAnnotationProcessor'
25-08-04.20:55:01.375 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.ProxyAsyncConfiguration'
25-08-04.20:55:01.394 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
25-08-04.20:55:01.396 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
25-08-04.20:55:01.398 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
25-08-04.20:55:01.406 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
25-08-04.20:55:01.407 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
25-08-04.20:55:01.410 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
25-08-04.20:55:01.410 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
25-08-04.20:55:01.410 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
25-08-04.20:55:01.410 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
25-08-04.20:55:01.422 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
25-08-04.20:55:01.430 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
25-08-04.20:55:01.430 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:55:01.563 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:55:01.563 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
25-08-04.20:55:01.610 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
25-08-04.20:55:01.610 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
25-08-04.20:55:01.613 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
25-08-04.20:55:01.613 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
25-08-04.20:55:01.614 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.624 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
25-08-04.20:55:01.632 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.634 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslBundleRegistry'
25-08-04.20:55:01.634 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
25-08-04.20:55:01.636 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:55:01.638 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:01.638 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:55:01.640 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
25-08-04.20:55:01.640 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'fileWatcher'
25-08-04.20:55:01.641 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
25-08-04.20:55:01.647 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
25-08-04.20:55:01.647 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.648 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
25-08-04.20:55:01.648 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
25-08-04.20:55:01.649 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
25-08-04.20:55:01.649 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.650 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
25-08-04.20:55:01.650 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
25-08-04.20:55:01.651 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.687 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
25-08-04.20:55:01.687 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
25-08-04.20:55:01.687 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:55:01.688 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
25-08-04.20:55:01.688 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
25-08-04.20:55:01.689 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
25-08-04.20:55:01.689 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
25-08-04.20:55:01.689 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:55:01.693 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:55:01.706 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
25-08-04.20:55:01.706 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:55:01.707 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
25-08-04.20:55:01.707 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
25-08-04.20:55:01.708 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:55:01.710 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:55:01.716 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
25-08-04.20:55:01.834 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-04.20:55:01.848 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-04.20:55:01.849 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-04.20:55:01.849 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-04.20:55:01.964 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-04.20:55:01.964 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2119 ms
25-08-04.20:55:01.969 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
25-08-04.20:55:01.973 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
25-08-04.20:55:01.973 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
25-08-04.20:55:01.978 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
25-08-04.20:55:02.040 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'application'
25-08-04.20:55:02.041 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientInitializer'
25-08-04.20:55:02.048 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultArmoryStrategyFactory'
25-08-04.20:55:02.049 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'rootNode'
25-08-04.20:55:02.052 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolExecutor'
25-08-04.20:55:02.052 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolConfig'
25-08-04.20:55:02.053 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:55:02.057 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolExecutor' via factory method to bean named 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:55:02.060 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientRepositoryImpl'
25-08-04.20:55:02.066 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMapper'
25-08-04.20:55:02.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionFactory'
25-08-04.20:55:02.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
25-08-04.20:55:02.078 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
25-08-04.20:55:02.078 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:55:02.081 [main            ] DEBUG HikariConfig           - Driver class org.mariadb.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@63947c6b
25-08-04.20:55:02.086 [main            ] DEBUG HikariConfig           - MariaDB_HikariCP - configuration:
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - allowPoolSuspension.............false
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - autoCommit......................true
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - catalog.........................none
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - connectionInitSql...............none
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - connectionTestQuery............."SELECT 1"
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - connectionTimeout...............30000
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - dataSource......................none
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - dataSourceClassName.............none
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - dataSourceJNDI..................none
25-08-04.20:55:02.090 [main            ] DEBUG HikariConfig           - dataSourceProperties............{password=<masked>}
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - driverClassName................."org.mariadb.jdbc.Driver"
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - exceptionOverrideClassName......none
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - healthCheckProperties...........{}
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - healthCheckRegistry.............none
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - idleTimeout.....................180000
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - initializationFailTimeout.......1
25-08-04.20:55:02.092 [main            ] DEBUG HikariConfig           - isolateInternalQueries..........false
25-08-04.20:55:02.093 [main            ] DEBUG HikariConfig           - jdbcUrl.........................jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false
25-08-04.20:55:02.093 [main            ] DEBUG HikariConfig           - keepaliveTime...................0
25-08-04.20:55:02.093 [main            ] DEBUG HikariConfig           - leakDetectionThreshold..........0
25-08-04.20:55:02.093 [main            ] DEBUG HikariConfig           - maxLifetime.....................1800000
25-08-04.20:55:02.093 [main            ] DEBUG HikariConfig           - maximumPoolSize.................25
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - metricRegistry..................none
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - metricsTrackerFactory...........none
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - minimumIdle.....................15
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - password........................<masked>
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - poolName........................"MariaDB_HikariCP"
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - readOnly........................false
25-08-04.20:55:02.094 [main            ] DEBUG HikariConfig           - registerMbeans..................false
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - scheduledExecutor...............none
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - schema..........................none
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - threadFactory...................internal
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - transactionIsolation............default
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - username........................"root"
25-08-04.20:55:02.095 [main            ] DEBUG HikariConfig           - validationTimeout...............5000
25-08-04.20:55:02.098 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-04.20:55:02.542 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@362a561e
25-08-04.20:55:02.551 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-04.20:55:02.552 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:55:02.564 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
25-08-04.20:55:02.658 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:55:02.659 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:55:02.762 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@120f7c7f
25-08-04.20:55:02.800 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=2, active=0, idle=2, waiting=0)
25-08-04.20:55:02.837 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
25-08-04.20:55:02.837 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
25-08-04.20:55:02.838 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:55:02.840 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'dataSource'
25-08-04.20:55:02.840 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:55:02.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionTemplate'
25-08-04.20:55:02.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
25-08-04.20:55:02.846 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:55:02.849 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:55:02.849 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:02.852 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
25-08-04.20:55:02.860 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRelMapper'
25-08-04.20:55:02.862 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMcpRelMapper'
25-08-04.20:55:02.884 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptRepositoryImpl'
25-08-04.20:55:02.886 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptMapper'
25-08-04.20:55:02.892 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelRepositoryImpl'
25-08-04.20:55:02.894 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelMapper'
25-08-04.20:55:02.896 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelToolConfigMapper'
25-08-04.20:55:02.904 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRepositoryImpl'
25-08-04.20:55:02.906 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorMapper'
25-08-04.20:55:02.912 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpRepositoryImpl'
25-08-04.20:55:02.914 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpMapper'
25-08-04.20:55:02.921 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpNode'
25-08-04.20:55:02.921 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorNode'
25-08-04.20:55:02.922 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'vectorStore'
25-08-04.20:55:02.922 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcTemplate'
25-08-04.20:55:02.922 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'dataSource'
25-08-04.20:55:02.922 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@2546b786
25-08-04.20:55:02.929 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiEmbeddingModel'
25-08-04.20:55:02.929 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration'
25-08-04.20:55:02.930 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:02.932 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:55:02.938 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'retryTemplate'
25-08-04.20:55:02.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration'
25-08-04.20:55:02.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:55:02.940 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'retryTemplate' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:55:02.948 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'responseErrorHandler'
25-08-04.20:55:02.948 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'responseErrorHandler' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
25-08-04.20:55:02.950 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientBuilderConfigurer'
25-08-04.20:55:02.953 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
25-08-04.20:55:02.953 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
25-08-04.20:55:02.953 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:55:02.955 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:55:02.957 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=3, active=0, idle=3, waiting=0)
25-08-04.20:55:02.961 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
25-08-04.20:55:02.961 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:55:02.962 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
25-08-04.20:55:02.963 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'messageConverters'
25-08-04.20:55:02.963 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
25-08-04.20:55:02.966 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stringHttpMessageConverter'
25-08-04.20:55:02.966 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
25-08-04.20:55:02.966 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
25-08-04.20:55:02.971 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
25-08-04.20:55:02.971 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
25-08-04.20:55:02.971 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonObjectMapper'
25-08-04.20:55:02.971 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
25-08-04.20:55:02.972 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
25-08-04.20:55:02.972 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:55:02.972 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
25-08-04.20:55:02.972 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:55:02.975 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:55:02.976 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'parameterNamesModule'
25-08-04.20:55:02.976 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
25-08-04.20:55:02.980 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModule'
25-08-04.20:55:02.980 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
25-08-04.20:55:02.980 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModuleEntries'
25-08-04.20:55:02.980 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:02.980 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
25-08-04.20:55:03.006 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:03.006 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
25-08-04.20:55:03.008 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonComponentModule'
25-08-04.20:55:03.008 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
25-08-04.20:55:03.013 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:03.013 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:55:03.016 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
25-08-04.20:55:03.028 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:55:03.035 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientBuilder' via factory method to bean named 'restClientBuilderConfigurer'
25-08-04.20:55:03.082 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@19118016
25-08-04.20:55:03.115 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=4, active=0, idle=4, waiting=0)
25-08-04.20:55:03.231 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5fa06e9e
25-08-04.20:55:03.253 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration'
25-08-04.20:55:03.263 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnectorCustomizer'
25-08-04.20:55:03.263 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration'
25-08-04.20:55:03.264 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnector'
25-08-04.20:55:03.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdkClientHttpConnectorFactory'
25-08-04.20:55:03.266 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorFactoryConfiguration$JdkClient'
25-08-04.20:55:03.268 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnector' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:55:03.271 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=5, active=0, idle=5, waiting=0)
25-08-04.20:55:03.277 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnectorCustomizer' via factory method to bean named 'webClientHttpConnector'
25-08-04.20:55:03.279 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'exchangeStrategiesCustomizer'
25-08-04.20:55:03.279 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration$WebClientCodecsConfiguration'
25-08-04.20:55:03.280 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultCodecCustomizer'
25-08-04.20:55:03.280 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration'
25-08-04.20:55:03.282 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:55:03.283 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'defaultCodecCustomizer' via factory method to bean named 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:55:03.284 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
25-08-04.20:55:03.284 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
25-08-04.20:55:03.285 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:55:03.407 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3f6a0216
25-08-04.20:55:03.433 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'jdbcTemplate'
25-08-04.20:55:03.433 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'openAiEmbeddingModel'
25-08-04.20:55:03.443 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=6, active=0, idle=6, waiting=0)
25-08-04.20:55:03.563 [main            ] INFO  MariaDBVectorStore     - Using the vector table name: vector_store. Is empty: false
25-08-04.20:55:03.566 [main            ] INFO  MariaDBVectorStore     - Initializing MariaDBVectorStore schema for table: vector_store in schema: ai_agent_db
25-08-04.20:55:03.566 [main            ] INFO  MariaDBVectorStore     - vectorTableValidationsEnabled false
25-08-04.20:55:03.604 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@6dd92a19
25-08-04.20:55:03.646 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=7, active=1, idle=6, waiting=0)
25-08-04.20:55:03.695 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelNode'
25-08-04.20:55:03.696 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientNode'
25-08-04.20:55:03.706 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'guavaConfig'
25-08-04.20:55:03.706 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientController'
25-08-04.20:55:03.715 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageController'
25-08-04.20:55:03.718 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageServiceImpl'
25-08-04.20:55:03.720 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionManager'
25-08-04.20:55:03.721 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'dataSource'
25-08-04.20:55:03.728 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
25-08-04.20:55:03.731 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cache'
25-08-04.20:55:03.775 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
25-08-04.20:55:03.776 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
25-08-04.20:55:03.777 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
25-08-04.20:55:03.778 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
25-08-04.20:55:03.778 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
25-08-04.20:55:03.779 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
25-08-04.20:55:03.780 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:55:03.783 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:55:03.783 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@79149478
25-08-04.20:55:03.785 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
25-08-04.20:55:03.786 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:55:03.786 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
25-08-04.20:55:03.789 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
25-08-04.20:55:03.789 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
25-08-04.20:55:03.790 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'error'
25-08-04.20:55:03.792 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameViewResolver'
25-08-04.20:55:03.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
25-08-04.20:55:03.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:55:03.798 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:03.798 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:55:03.799 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'conventionErrorViewResolver'
25-08-04.20:55:03.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorAttributes'
25-08-04.20:55:03.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'basicErrorController'
25-08-04.20:55:03.803 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
25-08-04.20:55:03.809 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
25-08-04.20:55:03.810 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:55:03.810 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:55:03.810 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@bdecc21'
25-08-04.20:55:03.813 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
25-08-04.20:55:03.814 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:55:03.814 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:55:03.814 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@bdecc21'
25-08-04.20:55:03.818 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=8, active=0, idle=8, waiting=0)
25-08-04.20:55:03.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
25-08-04.20:55:03.819 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcConversionService'
25-08-04.20:55:03.824 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
25-08-04.20:55:03.827 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:03.827 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.827 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
25-08-04.20:55:03.846 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:03.846 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.846 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.852 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeResolver'
25-08-04.20:55:03.854 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'themeResolver'
25-08-04.20:55:03.856 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'flashMapManager'
25-08-04.20:55:03.859 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewNameTranslator'
25-08-04.20:55:03.860 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcValidator'
25-08-04.20:55:03.866 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-04.20:55:03.867 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
25-08-04.20:55:03.875 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
25-08-04.20:55:03.875 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:55:03.875 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.875 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.941 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPatternParser'
25-08-04.20:55:03.941 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUrlPathHelper'
25-08-04.20:55:03.942 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPathMatcher'
25-08-04.20:55:03.943 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
25-08-04.20:55:03.944 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.944 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.945 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameHandlerMapping'
25-08-04.20:55:03.946 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.946 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.950 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'routerFunctionMapping'
25-08-04.20:55:03.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.950 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.955 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'resourceHandlerMapping'
25-08-04.20:55:03.956 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:55:03.956 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.956 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:55:03.975 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
25-08-04.20:55:03.976 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
25-08-04.20:55:03.976 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:55:03.976 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:03.976 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
25-08-04.20:55:04.011 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcViewResolver'
25-08-04.20:55:04.011 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:55:04.013 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultViewResolver'
25-08-04.20:55:04.019 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:55:04.019 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@bdecc21'
25-08-04.20:55:04.021 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:55:04.042 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerFunctionAdapter'
25-08-04.20:55:04.043 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
25-08-04.20:55:04.043 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
25-08-04.20:55:04.043 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
25-08-04.20:55:04.044 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
25-08-04.20:55:04.044 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
25-08-04.20:55:04.045 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerExceptionResolver'
25-08-04.20:55:04.045 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:55:04.050 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
25-08-04.20:55:04.050 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
25-08-04.20:55:04.050 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
25-08-04.20:55:04.052 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
25-08-04.20:55:04.052 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
25-08-04.20:55:04.053 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
25-08-04.20:55:04.060 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.SseWebFluxTransportAutoConfiguration'
25-08-04.20:55:04.060 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webFluxClientTransports'
25-08-04.20:55:04.061 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:55:04.062 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webFluxClientTransports' via factory method to bean named 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:55:04.062 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:55:04.064 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.StdioTransportAutoConfiguration'
25-08-04.20:55:04.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stdioTransports'
25-08-04.20:55:04.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:55:04.066 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stdioTransports' via factory method to bean named 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:55:04.078 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpClientAutoConfiguration'
25-08-04.20:55:04.080 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClients'
25-08-04.20:55:04.080 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClientConfigurer'
25-08-04.20:55:04.084 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'mcpSyncClientConfigurer'
25-08-04.20:55:04.084 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:55:04.085 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'makeSyncClientsClosable'
25-08-04.20:55:04.087 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'makeSyncClientsClosable' via factory method to bean named 'mcpSyncClients'
25-08-04.20:55:04.088 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration'
25-08-04.20:55:04.088 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpToolCallbacks'
25-08-04.20:55:04.092 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:55:04.095 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration'
25-08-04.20:55:04.096 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatClientBuilderConfigurer'
25-08-04.20:55:04.097 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.client-org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderProperties'
25-08-04.20:55:04.099 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.memory.autoconfigure.ChatMemoryAutoConfiguration'
25-08-04.20:55:04.099 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemoryRepository'
25-08-04.20:55:04.102 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemory'
25-08-04.20:55:04.103 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'chatMemory' via factory method to bean named 'chatMemoryRepository'
25-08-04.20:55:04.107 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:55:04.109 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration'
25-08-04.20:55:04.110 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.observations-org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationProperties'
25-08-04.20:55:04.112 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.embedding.observation.autoconfigure.EmbeddingObservationAutoConfiguration'
25-08-04.20:55:04.113 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:55:04.114 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration'
25-08-04.20:55:04.114 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.image.observations-org.springframework.ai.model.image.observation.autoconfigure.ImageObservationProperties'
25-08-04.20:55:04.115 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration'
25-08-04.20:55:04.116 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gsonBuilder'
25-08-04.20:55:04.116 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardGsonBuilderCustomizer'
25-08-04.20:55:04.117 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:55:04.120 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardGsonBuilderCustomizer' via factory method to bean named 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:55:04.122 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gsonBuilder' via factory method to bean named 'standardGsonBuilderCustomizer'
25-08-04.20:55:04.146 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gson'
25-08-04.20:55:04.147 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gson' via factory method to bean named 'gsonBuilder'
25-08-04.20:55:04.172 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
25-08-04.20:55:04.173 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration'
25-08-04.20:55:04.173 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientSsl'
25-08-04.20:55:04.173 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:55:04.174 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientSsl'
25-08-04.20:55:04.174 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:55:04.175 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:55:04.175 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration'
25-08-04.20:55:04.175 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioSpeechModel'
25-08-04.20:55:04.176 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:55:04.179 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:04.179 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:55:04.179 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:04.179 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:04.187 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration'
25-08-04.20:55:04.187 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioTranscriptionModel'
25-08-04.20:55:04.188 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:55:04.190 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:04.190 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:55:04.190 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:04.190 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:04.198 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration'
25-08-04.20:55:04.199 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallbackResolver'
25-08-04.20:55:04.200 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@64d7b720'
25-08-04.20:55:04.200 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'mcpToolCallbacks'
25-08-04.20:55:04.205 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolExecutionExceptionProcessor'
25-08-04.20:55:04.206 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallingManager'
25-08-04.20:55:04.207 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolCallbackResolver'
25-08-04.20:55:04.207 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolExecutionExceptionProcessor'
25-08-04.20:55:04.212 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.tools-org.springframework.ai.model.tool.autoconfigure.ToolCallingProperties'
25-08-04.20:55:04.213 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiChatAutoConfiguration'
25-08-04.20:55:04.214 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiChatModel'
25-08-04.20:55:04.215 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:55:04.218 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:04.218 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:55:04.218 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'toolCallingManager'
25-08-04.20:55:04.218 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:04.218 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:04.235 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration'
25-08-04.20:55:04.235 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiImageModel'
25-08-04.20:55:04.236 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:55:04.238 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:04.238 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:55:04.238 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:04.238 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:04.248 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiModerationAutoConfiguration'
25-08-04.20:55:04.248 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiModerationModel'
25-08-04.20:55:04.248 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:55:04.251 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:55:04.251 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:55:04.251 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'retryTemplate'
25-08-04.20:55:04.251 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:55:04.257 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
25-08-04.20:55:04.257 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:55:04.258 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration' via constructor to bean named 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:55:04.259 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanExporter'
25-08-04.20:55:04.259 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectNamingStrategy'
25-08-04.20:55:04.261 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
25-08-04.20:55:04.261 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@bdecc21'
25-08-04.20:55:04.265 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanServer'
25-08-04.20:55:04.270 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
25-08-04.20:55:04.270 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
25-08-04.20:55:04.270 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'springApplicationAdminRegistrar' via factory method to bean named 'environment'
25-08-04.20:55:04.274 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration'
25-08-04.20:55:04.274 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
25-08-04.20:55:04.275 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
25-08-04.20:55:04.275 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'applicationAvailability'
25-08-04.20:55:04.277 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
25-08-04.20:55:04.278 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
25-08-04.20:55:04.278 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionExecutionListeners'
25-08-04.20:55:04.279 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
25-08-04.20:55:04.282 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
25-08-04.20:55:04.282 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
25-08-04.20:55:04.282 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleProcessor'
25-08-04.20:55:04.282 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:55:04.283 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:55:04.285 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
25-08-04.20:55:04.285 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
25-08-04.20:55:04.288 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:55:04.289 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:55:04.289 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration'
25-08-04.20:55:04.289 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
25-08-04.20:55:04.289 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
25-08-04.20:55:04.292 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
25-08-04.20:55:04.293 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
25-08-04.20:55:04.293 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
25-08-04.20:55:04.293 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcClient'
25-08-04.20:55:04.294 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'namedParameterJdbcTemplate'
25-08-04.20:55:04.295 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration'
25-08-04.20:55:04.295 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:55:04.296 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration' via constructor to bean named 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:55:04.296 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
25-08-04.20:55:04.296 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
25-08-04.20:55:04.296 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
25-08-04.20:55:04.297 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:55:04.298 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:55:04.298 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
25-08-04.20:55:04.299 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:55:04.299 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
25-08-04.20:55:04.300 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
25-08-04.20:55:04.302 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
25-08-04.20:55:04.302 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
25-08-04.20:55:04.302 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
25-08-04.20:55:04.303 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
25-08-04.20:55:04.303 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
25-08-04.20:55:04.303 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionTemplate'
25-08-04.20:55:04.303 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManager'
25-08-04.20:55:04.304 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
25-08-04.20:55:04.305 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
25-08-04.20:55:04.305 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
25-08-04.20:55:04.305 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartResolver'
25-08-04.20:55:04.328 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-04.20:55:04.339 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-04.20:55:04.349 [main            ] INFO  Application            - Started Application in 5.842 seconds (process running for 7.245)
25-08-04.20:55:04.352 [main            ] INFO  AiClientInitializer    - 开始自动初始化AI客户端...
25-08-04.20:55:04.352 [main            ] INFO  DefaultArmoryStrategyFactory - 开始执行AI客户端组装流程
25-08-04.20:55:04.352 [main            ] INFO  DefaultArmoryStrategyFactory - === 第1阶段：数据加载 ===
25-08-04.20:55:04.634 [main            ] INFO  RootNode               - 数据加载完成: clients=2, prompts=3, models=2, advisors=2, mcpTools=2
25-08-04.20:55:04.634 [main            ] INFO  DefaultArmoryStrategyFactory - === 第2阶段：MCP工具组装 ===
25-08-04.20:55:04.634 [main            ] INFO  AiClientToolMcpNode    - 开始构建MCP工具客户端，数量: 2
25-08-04.20:55:04.950 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@55867923
25-08-04.20:55:04.991 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=9, active=0, idle=9, waiting=0)
25-08-04.20:55:05.134 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@2c7eb58c
25-08-04.20:55:05.176 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=10, active=0, idle=10, waiting=0)
25-08-04.20:55:05.331 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@1b7cddf6
25-08-04.20:55:05.362 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=11, active=0, idle=11, waiting=0)
25-08-04.20:55:05.492 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@281dd3e6
25-08-04.20:55:05.533 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=12, active=0, idle=12, waiting=0)
25-08-04.20:55:05.648 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@14072aae
25-08-04.20:55:05.665 [HttpClient-8-Worker-1] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-04.20:55:05.690 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=13, active=0, idle=13, waiting=0)
25-08-04.20:55:05.836 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@5bfd6881
25-08-04.20:55:05.874 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=14, active=0, idle=14, waiting=0)
25-08-04.20:55:05.870 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-04.20:55:05.886 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_1
25-08-04.20:55:05.886 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_1 -> weather-tool
25-08-04.20:55:05.991 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@4fa3103a
25-08-04.20:55:06.030 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:55:06.298 [HttpClient-9-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=TencentMapWebService, version=1.0.0] and Instructions null
25-08-04.20:55:06.421 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], serverInfo=Implementation[name=TencentMapWebService, version=1.0.0], instructions=null]
25-08-04.20:55:06.422 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_2
25-08-04.20:55:06.422 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_2 -> local-tool
25-08-04.20:55:06.422 [main            ] INFO  AiClientToolMcpNode    - MCP工具客户端构建完成
25-08-04.20:55:06.424 [main            ] INFO  DefaultArmoryStrategyFactory - === 第3阶段：顾问组装 ===
25-08-04.20:55:06.424 [main            ] INFO  AiClientAdvisorNode    - 开始构建顾问，数量: 2
25-08-04.20:55:06.450 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_1
25-08-04.20:55:06.450 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_1 -> 记忆管理顾问 (ChatMemory)
25-08-04.20:55:06.508 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_2
25-08-04.20:55:06.509 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_2 -> RAG问答顾问 (RagAnswer)
25-08-04.20:55:06.509 [main            ] INFO  AiClientAdvisorNode    - 顾问构建完成
25-08-04.20:55:06.509 [main            ] INFO  DefaultArmoryStrategyFactory - === 第4阶段：模型组装 ===
25-08-04.20:55:06.509 [main            ] INFO  AiClientModelNode      - 开始构建模型客户端，数量: 2
25-08-04.20:55:06.515 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_2'
25-08-04.20:55:06.518 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_1'
25-08-04.20:55:06.962 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_1
25-08-04.20:55:06.962 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_1 -> qwen-plus (openai)
25-08-04.20:55:06.966 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_2
25-08-04.20:55:06.966 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_2 -> ollama (openai)
25-08-04.20:55:06.966 [main            ] INFO  AiClientModelNode      - 模型客户端构建完成
25-08-04.20:55:06.966 [main            ] INFO  DefaultArmoryStrategyFactory - === 第5阶段：AI客户端组装 ===
25-08-04.20:55:06.966 [main            ] INFO  AiClientNode           - 开始构建AI客户端，数量: 2
25-08-04.20:55:06.967 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientModel_2'
25-08-04.20:55:06.967 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientAdvisor_1'
25-08-04.20:55:28.777 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: ChatClient_2
25-08-04.20:55:28.777 [main            ] INFO  AiClientNode           - 成功注册AI客户端: ChatClient_2 -> clientId=2
25-08-04.20:55:28.777 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientModel_1'
25-08-04.20:55:28.778 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientAdvisor_2'
25-08-04.20:55:34.137 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:55:34.138 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:55:34.138 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:55:34.335 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: ChatClient_1
25-08-04.20:55:34.335 [main            ] INFO  AiClientNode           - 成功注册AI客户端: ChatClient_1 -> clientId=1
25-08-04.20:55:34.335 [main            ] INFO  AiClientNode           - AI客户端构建完成
25-08-04.20:55:34.335 [main            ] INFO  DefaultArmoryStrategyFactory - AI客户端组装流程完成
25-08-04.20:55:34.335 [main            ] INFO  AiClientInitializer    - AI客户端自动初始化完成
25-08-04.20:55:34.359 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@5d308dfd]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-04.20:55:34.365 [main            ] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-04.20:55:34.366 [tomcat-shutdown ] INFO  Http11NioProtocol      - Pausing ProtocolHandler ["http-nio-8091"]
25-08-04.20:55:34.906 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-04.20:55:34.913 [main            ] INFO  Http11NioProtocol      - Stopping ProtocolHandler ["http-nio-8091"]
25-08-04.20:55:34.931 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-04.20:55:34.931 [main            ] DEBUG HikariPool             - MariaDB_HikariCP - Before shutdown stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:55:34.932 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@362a561e: (connection evicted)
25-08-04.20:55:34.943 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@120f7c7f: (connection evicted)
25-08-04.20:55:34.958 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@2546b786: (connection evicted)
25-08-04.20:55:34.974 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@19118016: (connection evicted)
25-08-04.20:55:34.990 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5fa06e9e: (connection evicted)
25-08-04.20:55:35.005 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3f6a0216: (connection evicted)
25-08-04.20:55:35.022 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@6dd92a19: (connection evicted)
25-08-04.20:55:35.037 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@79149478: (connection evicted)
25-08-04.20:55:35.053 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@55867923: (connection evicted)
25-08-04.20:55:35.069 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@2c7eb58c: (connection evicted)
25-08-04.20:55:35.085 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@1b7cddf6: (connection evicted)
25-08-04.20:55:35.100 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@281dd3e6: (connection evicted)
25-08-04.20:55:35.116 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@14072aae: (connection evicted)
25-08-04.20:55:35.131 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@5bfd6881: (connection evicted)
25-08-04.20:55:35.146 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@4fa3103a: (connection evicted)
25-08-04.20:55:35.162 [main            ] DEBUG HikariPool             - MariaDB_HikariCP - After shutdown stats (total=0, active=0, idle=0, waiting=0)
25-08-04.20:55:35.163 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-04.20:55:35.167 [main            ] DEBUG DisposableBeanAdapter  - Custom destroy method 'shutdown' on bean with name 'threadPoolExecutor' completed
25-08-04.20:56:48.005 [main            ] INFO  Application            - Starting Application using Java 17.0.9 with PID 13604 (D:\code\ai-agent\ai-agent-app\target\classes started by 17813 in D:\code\ai-agent)
25-08-04.20:56:48.008 [main            ] INFO  Application            - The following 1 profile is active: "dev"
25-08-04.20:56:48.094 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
25-08-04.20:56:48.123 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
25-08-04.20:56:49.012 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.config.DataSourceConfig#MapperScannerRegistrar#0'
25-08-04.20:56:49.016 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cn.iflytek.Application#MapperScannerRegistrar#0'
25-08-04.20:56:49.017 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
25-08-04.20:56:49.042 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.043 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-04.20:56:49.044 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-04.20:56:49.144 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor'
25-08-04.20:56:49.178 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
25-08-04.20:56:49.179 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
25-08-04.20:56:49.180 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'forceAutoProxyCreatorToUseClassProxying'
25-08-04.20:56:49.180 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
25-08-04.20:56:49.180 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
25-08-04.20:56:49.184 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
25-08-04.20:56:49.186 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
25-08-04.20:56:49.190 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
25-08-04.20:56:49.190 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.internalConfigurationPropertiesBinder'
25-08-04.20:56:49.192 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAsyncAnnotationProcessor'
25-08-04.20:56:49.192 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.scheduling.annotation.ProxyAsyncConfiguration'
25-08-04.20:56:49.208 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
25-08-04.20:56:49.210 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
25-08-04.20:56:49.212 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
25-08-04.20:56:49.218 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
25-08-04.20:56:49.218 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
25-08-04.20:56:49.222 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
25-08-04.20:56:49.222 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
25-08-04.20:56:49.222 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
25-08-04.20:56:49.222 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
25-08-04.20:56:49.229 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionAttributeSource'
25-08-04.20:56:49.233 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionInterceptor'
25-08-04.20:56:49.234 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionInterceptor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:56:49.350 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionAttributeSource'
25-08-04.20:56:49.350 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.transaction.config.internalTransactionAdvisor' via factory method to bean named 'transactionInterceptor'
25-08-04.20:56:49.397 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
25-08-04.20:56:49.397 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
25-08-04.20:56:49.399 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
25-08-04.20:56:49.399 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
25-08-04.20:56:49.400 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.410 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.BoundConfigurationProperties'
25-08-04.20:56:49.421 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.424 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslBundleRegistry'
25-08-04.20:56:49.424 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration'
25-08-04.20:56:49.427 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:56:49.428 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:49.428 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration' via constructor to bean named 'spring.ssl-org.springframework.boot.autoconfigure.ssl.SslProperties'
25-08-04.20:56:49.431 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sslPropertiesSslBundleRegistrar'
25-08-04.20:56:49.432 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'fileWatcher'
25-08-04.20:56:49.433 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sslPropertiesSslBundleRegistrar' via factory method to bean named 'fileWatcher'
25-08-04.20:56:49.440 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
25-08-04.20:56:49.440 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.441 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
25-08-04.20:56:49.441 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
25-08-04.20:56:49.442 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
25-08-04.20:56:49.442 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.445 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
25-08-04.20:56:49.446 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
25-08-04.20:56:49.446 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.491 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorPageCustomizer'
25-08-04.20:56:49.491 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
25-08-04.20:56:49.492 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
25-08-04.20:56:49.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServletRegistration'
25-08-04.20:56:49.492 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
25-08-04.20:56:49.493 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dispatcherServlet'
25-08-04.20:56:49.493 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
25-08-04.20:56:49.493 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:56:49.497 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServlet' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:56:49.511 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
25-08-04.20:56:49.511 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:56:49.512 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartConfigElement'
25-08-04.20:56:49.512 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
25-08-04.20:56:49.513 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:56:49.516 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
25-08-04.20:56:49.521 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'errorPageCustomizer' via factory method to bean named 'dispatcherServletRegistration'
25-08-04.20:56:49.661 [main            ] INFO  TomcatWebServer        - Tomcat initialized with port 8091 (http)
25-08-04.20:56:49.682 [main            ] INFO  Http11NioProtocol      - Initializing ProtocolHandler ["http-nio-8091"]
25-08-04.20:56:49.684 [main            ] INFO  StandardService        - Starting service [Tomcat]
25-08-04.20:56:49.684 [main            ] INFO  StandardEngine         - Starting Servlet engine: [Apache Tomcat/10.1.36]
25-08-04.20:56:49.784 [main            ] INFO  [/]                    - Initializing Spring embedded WebApplicationContext
25-08-04.20:56:49.784 [main            ] INFO  ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1711 ms
25-08-04.20:56:49.787 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestContextFilter'
25-08-04.20:56:49.791 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'formContentFilter'
25-08-04.20:56:49.791 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
25-08-04.20:56:49.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'characterEncodingFilter'
25-08-04.20:56:49.838 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'application'
25-08-04.20:56:49.839 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientInitializer'
25-08-04.20:56:49.843 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultArmoryStrategyFactory'
25-08-04.20:56:49.844 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'rootNode'
25-08-04.20:56:49.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolExecutor'
25-08-04.20:56:49.845 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolConfig'
25-08-04.20:56:49.846 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:56:49.848 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolExecutor' via factory method to bean named 'thread.pool.executor.config-cn.iflytek.config.ThreadPoolConfigProperties'
25-08-04.20:56:49.850 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientRepositoryImpl'
25-08-04.20:56:49.855 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMapper'
25-08-04.20:56:49.858 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionFactory'
25-08-04.20:56:49.858 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceConfig'
25-08-04.20:56:49.862 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSource'
25-08-04.20:56:49.863 [main            ] INFO  DataSourceConfig       - 创建数据源 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:56:49.865 [main            ] DEBUG HikariConfig           - Driver class org.mariadb.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@63947c6b
25-08-04.20:56:49.870 [main            ] DEBUG HikariConfig           - MariaDB_HikariCP - configuration:
25-08-04.20:56:49.875 [main            ] DEBUG HikariConfig           - allowPoolSuspension.............false
25-08-04.20:56:49.875 [main            ] DEBUG HikariConfig           - autoCommit......................true
25-08-04.20:56:49.875 [main            ] DEBUG HikariConfig           - catalog.........................none
25-08-04.20:56:49.875 [main            ] DEBUG HikariConfig           - connectionInitSql...............none
25-08-04.20:56:49.875 [main            ] DEBUG HikariConfig           - connectionTestQuery............."SELECT 1"
25-08-04.20:56:49.876 [main            ] DEBUG HikariConfig           - connectionTimeout...............30000
25-08-04.20:56:49.876 [main            ] DEBUG HikariConfig           - dataSource......................none
25-08-04.20:56:49.876 [main            ] DEBUG HikariConfig           - dataSourceClassName.............none
25-08-04.20:56:49.876 [main            ] DEBUG HikariConfig           - dataSourceJNDI..................none
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - dataSourceProperties............{password=<masked>}
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - driverClassName................."org.mariadb.jdbc.Driver"
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - exceptionOverrideClassName......none
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - healthCheckProperties...........{}
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - healthCheckRegistry.............none
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - idleTimeout.....................180000
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - initializationFailTimeout.......1
25-08-04.20:56:49.877 [main            ] DEBUG HikariConfig           - isolateInternalQueries..........false
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - jdbcUrl.........................jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - keepaliveTime...................0
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - leakDetectionThreshold..........0
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - maxLifetime.....................1800000
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - maximumPoolSize.................25
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - metricRegistry..................none
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - metricsTrackerFactory...........none
25-08-04.20:56:49.878 [main            ] DEBUG HikariConfig           - minimumIdle.....................15
25-08-04.20:56:49.879 [main            ] DEBUG HikariConfig           - password........................<masked>
25-08-04.20:56:49.879 [main            ] DEBUG HikariConfig           - poolName........................"MariaDB_HikariCP"
25-08-04.20:56:49.879 [main            ] DEBUG HikariConfig           - readOnly........................false
25-08-04.20:56:49.879 [main            ] DEBUG HikariConfig           - registerMbeans..................false
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - scheduledExecutor...............none
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - schema..........................none
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - threadFactory...................internal
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - transactionIsolation............default
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - username........................"root"
25-08-04.20:56:49.880 [main            ] DEBUG HikariConfig           - validationTimeout...............5000
25-08-04.20:56:49.883 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Starting...
25-08-04.20:56:50.220 [main            ] INFO  HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@42107318
25-08-04.20:56:50.229 [main            ] INFO  HikariDataSource       - MariaDB_HikariCP - Start completed.
25-08-04.20:56:50.229 [main            ] INFO  DataSourceConfig       - 数据源创建成功 - URL: jdbc:mariadb://************:3306/ai_agent_db?useUnicode=true&characterEncoding=utf8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&serverTimezone=UTC&useSSL=false, 驱动: org.mariadb.jdbc.Driver
25-08-04.20:56:50.238 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
25-08-04.20:56:50.342 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:56:50.342 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=1, active=0, idle=1, waiting=0)
25-08-04.20:56:50.456 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@46dedde9
25-08-04.20:56:50.495 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=2, active=0, idle=2, waiting=0)
25-08-04.20:56:50.524 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'dataSourceScriptDatabaseInitializer'
25-08-04.20:56:50.524 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration'
25-08-04.20:56:50.526 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:56:50.530 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'dataSource'
25-08-04.20:56:50.531 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'dataSourceScriptDatabaseInitializer' via factory method to bean named 'spring.sql.init-org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties'
25-08-04.20:56:50.539 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'sqlSessionTemplate'
25-08-04.20:56:50.539 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
25-08-04.20:56:50.542 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:56:50.550 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
25-08-04.20:56:50.550 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:50.554 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
25-08-04.20:56:50.564 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRelMapper'
25-08-04.20:56:50.567 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientMcpRelMapper'
25-08-04.20:56:50.597 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptRepositoryImpl'
25-08-04.20:56:50.599 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientSystemPromptMapper'
25-08-04.20:56:50.610 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelRepositoryImpl'
25-08-04.20:56:50.612 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelMapper'
25-08-04.20:56:50.615 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelToolConfigMapper'
25-08-04.20:56:50.627 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorRepositoryImpl'
25-08-04.20:56:50.628 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorMapper'
25-08-04.20:56:50.635 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpRepositoryImpl'
25-08-04.20:56:50.638 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpMapper'
25-08-04.20:56:50.642 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@329517d6
25-08-04.20:56:50.647 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientToolMcpNode'
25-08-04.20:56:50.648 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientAdvisorNode'
25-08-04.20:56:50.649 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'vectorStore'
25-08-04.20:56:50.649 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcTemplate'
25-08-04.20:56:50.649 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcTemplate' via factory method to bean named 'dataSource'
25-08-04.20:56:50.657 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiEmbeddingModel'
25-08-04.20:56:50.657 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration'
25-08-04.20:56:50.658 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:50.659 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:56:50.666 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'retryTemplate'
25-08-04.20:56:50.666 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.retry.autoconfigure.SpringAiRetryAutoConfiguration'
25-08-04.20:56:50.667 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:56:50.668 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'retryTemplate' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:56:50.678 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'responseErrorHandler'
25-08-04.20:56:50.678 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'responseErrorHandler' via factory method to bean named 'spring.ai.retry-org.springframework.ai.retry.autoconfigure.SpringAiRetryProperties'
25-08-04.20:56:50.680 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=3, active=0, idle=3, waiting=0)
25-08-04.20:56:50.680 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:50.680 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'spring.ai.openai.embedding-org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingProperties'
25-08-04.20:56:50.680 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:50.680 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiEmbeddingModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:50.681 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestClientAutoConfiguration'
25-08-04.20:56:50.683 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientBuilderConfigurer'
25-08-04.20:56:50.685 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactoryBuilder'
25-08-04.20:56:50.685 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.client.HttpClientAutoConfiguration'
25-08-04.20:56:50.686 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:56:50.687 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactoryBuilder' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:56:50.692 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'clientHttpRequestFactorySettings'
25-08-04.20:56:50.692 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'clientHttpRequestFactorySettings' via factory method to bean named 'spring.http.client-org.springframework.boot.autoconfigure.http.client.HttpClientProperties'
25-08-04.20:56:50.693 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpMessageConvertersRestClientCustomizer'
25-08-04.20:56:50.693 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'messageConverters'
25-08-04.20:56:50.693 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
25-08-04.20:56:50.697 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stringHttpMessageConverter'
25-08-04.20:56:50.697 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
25-08-04.20:56:50.697 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stringHttpMessageConverter' via factory method to bean named 'environment'
25-08-04.20:56:50.703 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
25-08-04.20:56:50.703 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
25-08-04.20:56:50.704 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonObjectMapper'
25-08-04.20:56:50.704 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
25-08-04.20:56:50.704 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
25-08-04.20:56:50.705 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:56:50.705 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
25-08-04.20:56:50.705 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:56:50.708 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
25-08-04.20:56:50.709 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'parameterNamesModule'
25-08-04.20:56:50.709 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
25-08-04.20:56:50.712 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModule'
25-08-04.20:56:50.712 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonMixinConfiguration'
25-08-04.20:56:50.713 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonMixinModuleEntries'
25-08-04.20:56:50.713 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModuleEntries' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:50.713 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
25-08-04.20:56:50.742 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:50.742 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jsonMixinModule' via factory method to bean named 'jsonMixinModuleEntries'
25-08-04.20:56:50.744 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jsonComponentModule'
25-08-04.20:56:50.744 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
25-08-04.20:56:50.748 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:50.748 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
25-08-04.20:56:50.750 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
25-08-04.20:56:50.767 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:56:50.780 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientBuilder' via factory method to bean named 'restClientBuilderConfigurer'
25-08-04.20:56:50.804 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@31ec20e5
25-08-04.20:56:50.835 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=4, active=0, idle=4, waiting=0)
25-08-04.20:56:50.951 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@2fdbe14d
25-08-04.20:56:50.991 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=5, active=0, idle=5, waiting=0)
25-08-04.20:56:51.001 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration'
25-08-04.20:56:51.005 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnectorCustomizer'
25-08-04.20:56:51.005 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorAutoConfiguration'
25-08-04.20:56:51.006 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientHttpConnector'
25-08-04.20:56:51.007 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdkClientHttpConnectorFactory'
25-08-04.20:56:51.007 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.ClientHttpConnectorFactoryConfiguration$JdkClient'
25-08-04.20:56:51.009 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnector' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:56:51.015 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientHttpConnectorCustomizer' via factory method to bean named 'webClientHttpConnector'
25-08-04.20:56:51.016 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'exchangeStrategiesCustomizer'
25-08-04.20:56:51.016 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.reactive.function.client.WebClientAutoConfiguration$WebClientCodecsConfiguration'
25-08-04.20:56:51.018 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultCodecCustomizer'
25-08-04.20:56:51.018 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$DefaultCodecsConfiguration'
25-08-04.20:56:51.019 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:56:51.020 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'defaultCodecCustomizer' via factory method to bean named 'spring.codec-org.springframework.boot.autoconfigure.codec.CodecProperties'
25-08-04.20:56:51.020 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
25-08-04.20:56:51.021 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
25-08-04.20:56:51.021 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
25-08-04.20:56:51.163 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'jdbcTemplate'
25-08-04.20:56:51.163 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'vectorStore' via factory method to bean named 'openAiEmbeddingModel'
25-08-04.20:56:51.306 [main            ] INFO  MariaDBVectorStore     - Using the vector table name: vector_store. Is empty: false
25-08-04.20:56:51.309 [main            ] INFO  MariaDBVectorStore     - Initializing MariaDBVectorStore schema for table: vector_store in schema: ai_agent_db
25-08-04.20:56:51.309 [main            ] INFO  MariaDBVectorStore     - vectorTableValidationsEnabled false
25-08-04.20:56:51.416 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientModelNode'
25-08-04.20:56:51.420 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientNode'
25-08-04.20:56:51.437 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'guavaConfig'
25-08-04.20:56:51.439 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'aiClientController'
25-08-04.20:56:51.456 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageController'
25-08-04.20:56:51.463 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ragStorageServiceImpl'
25-08-04.20:56:51.471 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionManager'
25-08-04.20:56:51.472 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'dataSource'
25-08-04.20:56:51.490 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar.methodValidationExcludeFilter'
25-08-04.20:56:51.495 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'cache'
25-08-04.20:56:51.524 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
25-08-04.20:56:51.524 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
25-08-04.20:56:51.525 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
25-08-04.20:56:51.525 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
25-08-04.20:56:51.526 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration'
25-08-04.20:56:51.526 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskExecutorBuilder'
25-08-04.20:56:51.527 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:56:51.529 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskExecutorBuilder' via factory method to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:56:51.531 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration'
25-08-04.20:56:51.531 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
25-08-04.20:56:51.531 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskExecutorBuilder'
25-08-04.20:56:51.533 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
25-08-04.20:56:51.534 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
25-08-04.20:56:51.535 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'error'
25-08-04.20:56:51.537 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameViewResolver'
25-08-04.20:56:51.538 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
25-08-04.20:56:51.539 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:56:51.542 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:51.542 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:56:51.543 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'conventionErrorViewResolver'
25-08-04.20:56:51.544 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'errorAttributes'
25-08-04.20:56:51.546 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'basicErrorController'
25-08-04.20:56:51.546 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
25-08-04.20:56:51.550 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
25-08-04.20:56:51.551 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:56:51.551 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:56:51.551 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:56:51.553 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
25-08-04.20:56:51.553 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.web-org.springframework.boot.autoconfigure.web.WebProperties'
25-08-04.20:56:51.553 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
25-08-04.20:56:51.554 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:56:51.558 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
25-08-04.20:56:51.558 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcConversionService'
25-08-04.20:56:51.562 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
25-08-04.20:56:51.564 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:51.565 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.565 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.578 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'welcomePageNotAcceptableHandlerMapping'
25-08-04.20:56:51.578 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:51.578 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.578 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'welcomePageNotAcceptableHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.584 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'localeResolver'
25-08-04.20:56:51.585 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'themeResolver'
25-08-04.20:56:51.586 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'flashMapManager'
25-08-04.20:56:51.588 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewNameTranslator'
25-08-04.20:56:51.588 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcValidator'
25-08-04.20:56:51.594 [main            ] INFO  OptionalValidatorFactoryBean - Failed to set up a Bean Validation provider: jakarta.validation.NoProviderFoundException: Unable to create a Configuration, because no Jakarta Bean Validation provider could be found. Add a provider like Hibernate Validator (RI) to your classpath.
25-08-04.20:56:51.595 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
25-08-04.20:56:51.600 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
25-08-04.20:56:51.600 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:56:51.600 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.600 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.677 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPatternParser'
25-08-04.20:56:51.678 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUrlPathHelper'
25-08-04.20:56:51.679 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcPathMatcher'
25-08-04.20:56:51.680 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
25-08-04.20:56:51.681 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.681 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewControllerHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.682 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'beanNameHandlerMapping'
25-08-04.20:56:51.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.682 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'beanNameHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.687 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'routerFunctionMapping'
25-08-04.20:56:51.687 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.687 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'routerFunctionMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.693 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'resourceHandlerMapping'
25-08-04.20:56:51.694 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:56:51.694 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.694 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'resourceHandlerMapping' via factory method to bean named 'mvcResourceUrlProvider'
25-08-04.20:56:51.714 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
25-08-04.20:56:51.714 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
25-08-04.20:56:51.715 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:56:51.715 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.715 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'requestMappingHandlerAdapter' via factory method to bean named 'mvcValidator'
25-08-04.20:56:51.760 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcViewResolver'
25-08-04.20:56:51.760 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcViewResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:56:51.762 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'defaultViewResolver'
25-08-04.20:56:51.768 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:56:51.768 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:56:51.770 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'viewResolver'
25-08-04.20:56:51.790 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerFunctionAdapter'
25-08-04.20:56:51.792 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
25-08-04.20:56:51.793 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'mvcConversionService'
25-08-04.20:56:51.793 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mvcUriComponentsContributor' via factory method to bean named 'requestMappingHandlerAdapter'
25-08-04.20:56:51.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
25-08-04.20:56:51.794 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
25-08-04.20:56:51.795 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'handlerExceptionResolver'
25-08-04.20:56:51.795 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'handlerExceptionResolver' via factory method to bean named 'mvcContentNegotiationManager'
25-08-04.20:56:51.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
25-08-04.20:56:51.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
25-08-04.20:56:51.801 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
25-08-04.20:56:51.802 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
25-08-04.20:56:51.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
25-08-04.20:56:51.803 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
25-08-04.20:56:51.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.SseWebFluxTransportAutoConfiguration'
25-08-04.20:56:51.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webFluxClientTransports'
25-08-04.20:56:51.810 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:56:51.812 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webFluxClientTransports' via factory method to bean named 'spring.ai.mcp.client.sse-org.springframework.ai.mcp.client.autoconfigure.properties.McpSseClientProperties'
25-08-04.20:56:51.814 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:56:51.817 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.StdioTransportAutoConfiguration'
25-08-04.20:56:51.817 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'stdioTransports'
25-08-04.20:56:51.818 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:56:51.819 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'stdioTransports' via factory method to bean named 'spring.ai.mcp.client.stdio-org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties'
25-08-04.20:56:51.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpClientAutoConfiguration'
25-08-04.20:56:51.820 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClients'
25-08-04.20:56:51.821 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpSyncClientConfigurer'
25-08-04.20:56:51.823 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'mcpSyncClientConfigurer'
25-08-04.20:56:51.823 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mcpSyncClients' via factory method to bean named 'spring.ai.mcp.client-org.springframework.ai.mcp.client.autoconfigure.properties.McpClientCommonProperties'
25-08-04.20:56:51.824 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'makeSyncClientsClosable'
25-08-04.20:56:51.824 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'makeSyncClientsClosable' via factory method to bean named 'mcpSyncClients'
25-08-04.20:56:51.825 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.mcp.client.autoconfigure.McpToolCallbackAutoConfiguration'
25-08-04.20:56:51.825 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mcpToolCallbacks'
25-08-04.20:56:51.828 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:56:51.830 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration'
25-08-04.20:56:51.830 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatClientBuilderConfigurer'
25-08-04.20:56:51.831 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.client-org.springframework.ai.model.chat.client.autoconfigure.ChatClientBuilderProperties'
25-08-04.20:56:51.832 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.memory.autoconfigure.ChatMemoryAutoConfiguration'
25-08-04.20:56:51.832 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemoryRepository'
25-08-04.20:56:51.833 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'chatMemory'
25-08-04.20:56:51.834 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'chatMemory' via factory method to bean named 'chatMemoryRepository'
25-08-04.20:56:51.836 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:56:51.837 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationAutoConfiguration'
25-08-04.20:56:51.838 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.chat.observations-org.springframework.ai.model.chat.observation.autoconfigure.ChatObservationProperties'
25-08-04.20:56:51.839 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.embedding.observation.autoconfigure.EmbeddingObservationAutoConfiguration'
25-08-04.20:56:51.840 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration$TracerNotPresentObservationConfiguration'
25-08-04.20:56:51.842 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.image.observation.autoconfigure.ImageObservationAutoConfiguration'
25-08-04.20:56:51.842 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.image.observations-org.springframework.ai.model.image.observation.autoconfigure.ImageObservationProperties'
25-08-04.20:56:51.843 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration'
25-08-04.20:56:51.843 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gsonBuilder'
25-08-04.20:56:51.844 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'standardGsonBuilderCustomizer'
25-08-04.20:56:51.844 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:56:51.847 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'standardGsonBuilderCustomizer' via factory method to bean named 'spring.gson-org.springframework.boot.autoconfigure.gson.GsonProperties'
25-08-04.20:56:51.847 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gsonBuilder' via factory method to bean named 'standardGsonBuilderCustomizer'
25-08-04.20:56:51.865 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'gson'
25-08-04.20:56:51.865 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'gson' via factory method to bean named 'gsonBuilder'
25-08-04.20:56:51.890 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
25-08-04.20:56:51.892 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.GsonHttpMessageConvertersConfiguration'
25-08-04.20:56:51.892 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'restClientSsl'
25-08-04.20:56:51.892 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'restClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:56:51.893 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'webClientSsl'
25-08-04.20:56:51.894 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'jdkClientHttpConnectorFactory'
25-08-04.20:56:51.894 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'webClientSsl' via factory method to bean named 'sslBundleRegistry'
25-08-04.20:56:51.894 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration'
25-08-04.20:56:51.895 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioSpeechModel'
25-08-04.20:56:51.895 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:56:51.899 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:51.899 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'spring.ai.openai.audio.speech-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechProperties'
25-08-04.20:56:51.899 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:51.899 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioSpeechModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:51.909 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration'
25-08-04.20:56:51.909 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiAudioTranscriptionModel'
25-08-04.20:56:51.910 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:56:51.914 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:51.914 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'spring.ai.openai.audio.transcription-org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionProperties'
25-08-04.20:56:51.914 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:51.914 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiAudioTranscriptionModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:51.921 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.tool.autoconfigure.ToolCallingAutoConfiguration'
25-08-04.20:56:51.922 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallbackResolver'
25-08-04.20:56:51.923 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@30865a90'
25-08-04.20:56:51.923 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallbackResolver' via factory method to bean named 'mcpToolCallbacks'
25-08-04.20:56:51.930 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolExecutionExceptionProcessor'
25-08-04.20:56:51.932 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'toolCallingManager'
25-08-04.20:56:51.932 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolCallbackResolver'
25-08-04.20:56:51.932 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'toolCallingManager' via factory method to bean named 'toolExecutionExceptionProcessor'
25-08-04.20:56:51.938 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.tools-org.springframework.ai.model.tool.autoconfigure.ToolCallingProperties'
25-08-04.20:56:51.939 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiChatAutoConfiguration'
25-08-04.20:56:51.940 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiChatModel'
25-08-04.20:56:51.941 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:56:51.945 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:51.945 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'spring.ai.openai.chat-org.springframework.ai.model.openai.autoconfigure.OpenAiChatProperties'
25-08-04.20:56:51.945 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'toolCallingManager'
25-08-04.20:56:51.945 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:51.945 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiChatModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:51.977 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration'
25-08-04.20:56:51.978 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiImageModel'
25-08-04.20:56:51.979 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:56:51.985 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:51.985 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'spring.ai.openai.image-org.springframework.ai.model.openai.autoconfigure.OpenAiImageProperties'
25-08-04.20:56:51.985 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:51.985 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiImageModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:51.997 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.ai.model.openai.autoconfigure.OpenAiModerationAutoConfiguration'
25-08-04.20:56:51.998 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'openAiModerationModel'
25-08-04.20:56:51.999 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:56:52.002 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai-org.springframework.ai.model.openai.autoconfigure.OpenAiConnectionProperties'
25-08-04.20:56:52.002 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'spring.ai.openai.moderation-org.springframework.ai.model.openai.autoconfigure.OpenAiModerationProperties'
25-08-04.20:56:52.002 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'retryTemplate'
25-08-04.20:56:52.002 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'openAiModerationModel' via factory method to bean named 'responseErrorHandler'
25-08-04.20:56:52.010 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
25-08-04.20:56:52.012 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:56:52.014 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration' via constructor to bean named 'spring.jmx-org.springframework.boot.autoconfigure.jmx.JmxProperties'
25-08-04.20:56:52.014 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanExporter'
25-08-04.20:56:52.015 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'objectNamingStrategy'
25-08-04.20:56:52.017 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
25-08-04.20:56:52.017 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@189b5fb1'
25-08-04.20:56:52.023 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'mbeanServer'
25-08-04.20:56:52.037 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
25-08-04.20:56:52.037 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
25-08-04.20:56:52.037 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'springApplicationAdminRegistrar' via factory method to bean named 'environment'
25-08-04.20:56:52.041 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration'
25-08-04.20:56:52.041 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.aop.AopAutoConfiguration'
25-08-04.20:56:52.041 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration'
25-08-04.20:56:52.042 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'applicationAvailability'
25-08-04.20:56:52.044 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizationAutoConfiguration'
25-08-04.20:56:52.044 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
25-08-04.20:56:52.045 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionExecutionListeners'
25-08-04.20:56:52.046 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
25-08-04.20:56:52.049 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
25-08-04.20:56:52.050 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration'
25-08-04.20:56:52.050 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'lifecycleProcessor'
25-08-04.20:56:52.051 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:56:52.051 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'lifecycleProcessor' via factory method to bean named 'spring.lifecycle-org.springframework.boot.autoconfigure.context.LifecycleProperties'
25-08-04.20:56:52.055 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
25-08-04.20:56:52.055 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
25-08-04.20:56:52.057 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:56:52.057 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
25-08-04.20:56:52.058 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.NamedParameterJdbcTemplateConfiguration'
25-08-04.20:56:52.058 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
25-08-04.20:56:52.058 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
25-08-04.20:56:52.061 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
25-08-04.20:56:52.062 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
25-08-04.20:56:52.062 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcClientAutoConfiguration'
25-08-04.20:56:52.062 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'jdbcClient'
25-08-04.20:56:52.062 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'jdbcClient' via factory method to bean named 'namedParameterJdbcTemplate'
25-08-04.20:56:52.063 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration'
25-08-04.20:56:52.064 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:56:52.064 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.reactor.ReactorAutoConfiguration' via constructor to bean named 'spring.reactor-org.springframework.boot.autoconfigure.reactor.ReactorProperties'
25-08-04.20:56:52.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration'
25-08-04.20:56:52.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration'
25-08-04.20:56:52.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'threadPoolTaskSchedulerBuilder'
25-08-04.20:56:52.065 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:56:52.066 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'threadPoolTaskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:56:52.067 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration'
25-08-04.20:56:52.068 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration' via constructor to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
25-08-04.20:56:52.068 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'simpleAsyncTaskSchedulerBuilder'
25-08-04.20:56:52.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
25-08-04.20:56:52.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$JdbcTransactionManagerConfiguration'
25-08-04.20:56:52.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
25-08-04.20:56:52.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
25-08-04.20:56:52.070 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
25-08-04.20:56:52.071 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
25-08-04.20:56:52.071 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'transactionTemplate'
25-08-04.20:56:52.071 [main            ] DEBUG DefaultListableBeanFactory - Autowiring by type from bean name 'transactionTemplate' via factory method to bean named 'transactionManager'
25-08-04.20:56:52.072 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
25-08-04.20:56:52.073 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
25-08-04.20:56:52.073 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
25-08-04.20:56:52.073 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'multipartResolver'
25-08-04.20:56:52.089 [main            ] INFO  Http11NioProtocol      - Starting ProtocolHandler ["http-nio-8091"]
25-08-04.20:56:52.101 [main            ] INFO  TomcatWebServer        - Tomcat started on port 8091 (http) with context path '/'
25-08-04.20:56:52.110 [main            ] INFO  Application            - Started Application in 4.97 seconds (process running for 6.348)
25-08-04.20:56:52.112 [main            ] INFO  AiClientInitializer    - 开始自动初始化AI客户端...
25-08-04.20:56:52.112 [main            ] INFO  DefaultArmoryStrategyFactory - 开始执行AI客户端组装流程
25-08-04.20:56:52.112 [main            ] INFO  DefaultArmoryStrategyFactory - === 第1阶段：数据加载 ===
25-08-04.20:56:52.122 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@2293e88c
25-08-04.20:56:52.154 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=6, active=5, idle=1, waiting=0)
25-08-04.20:56:52.407 [main            ] INFO  RootNode               - 数据加载完成: clients=2, prompts=3, models=2, advisors=2, mcpTools=2
25-08-04.20:56:52.407 [main            ] INFO  DefaultArmoryStrategyFactory - === 第2阶段：MCP工具组装 ===
25-08-04.20:56:52.407 [main            ] INFO  AiClientToolMcpNode    - 开始构建MCP工具客户端，数量: 2
25-08-04.20:56:53.256 [HttpClient-8-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], Info: Implementation[name=amap-sse-server, version=1.0.0] and Instructions null
25-08-04.20:56:53.306 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@daeeb8
25-08-04.20:56:53.350 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=7, active=0, idle=7, waiting=0)
25-08-04.20:56:53.440 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=LoggingCapabilities[], prompts=null, resources=null, tools=ToolCapabilities[listChanged=true]], serverInfo=Implementation[name=amap-sse-server, version=1.0.0], instructions=null]
25-08-04.20:56:53.457 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_1
25-08-04.20:56:53.457 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_1 -> weather-tool
25-08-04.20:56:53.482 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@4d253e55
25-08-04.20:56:53.523 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=8, active=0, idle=8, waiting=0)
25-08-04.20:56:53.877 [HttpClient-9-Worker-0] INFO  McpAsyncClient         - Server response with Protocol: 2024-11-05, Capabilities: ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], Info: Implementation[name=TencentMapWebService, version=1.0.0] and Instructions null
25-08-04.20:56:53.966 [main            ] INFO  AiClientToolMcpNode    - Tool SSE MCP Initialized InitializeResult[protocolVersion=2024-11-05, capabilities=ServerCapabilities[completions=null, experimental=null, logging=null, prompts=PromptCapabilities[listChanged=false], resources=null, tools=ToolCapabilities[listChanged=false]], serverInfo=Implementation[name=TencentMapWebService, version=1.0.0], instructions=null]
25-08-04.20:56:53.967 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientToolMcp_2
25-08-04.20:56:53.967 [main            ] INFO  AiClientToolMcpNode    - 成功注册MCP工具客户端: AiClientToolMcp_2 -> local-tool
25-08-04.20:56:53.967 [main            ] INFO  AiClientToolMcpNode    - MCP工具客户端构建完成
25-08-04.20:56:53.967 [main            ] INFO  DefaultArmoryStrategyFactory - === 第3阶段：顾问组装 ===
25-08-04.20:56:53.967 [main            ] INFO  AiClientAdvisorNode    - 开始构建顾问，数量: 2
25-08-04.20:56:53.977 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_1
25-08-04.20:56:53.977 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_1 -> 记忆管理顾问 (ChatMemory)
25-08-04.20:56:54.043 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientAdvisor_2
25-08-04.20:56:54.043 [main            ] INFO  AiClientAdvisorNode    - 成功注册顾问: AiClientAdvisor_2 -> RAG问答顾问 (RagAnswer)
25-08-04.20:56:54.043 [main            ] INFO  AiClientAdvisorNode    - 顾问构建完成
25-08-04.20:56:54.043 [main            ] INFO  DefaultArmoryStrategyFactory - === 第4阶段：模型组装 ===
25-08-04.20:56:54.043 [main            ] INFO  AiClientModelNode      - 开始构建模型客户端，数量: 2
25-08-04.20:56:54.049 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_2'
25-08-04.20:56:54.052 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientToolMcp_1'
25-08-04.20:56:54.565 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_1
25-08-04.20:56:54.566 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_1 -> qwen-plus (openai)
25-08-04.20:56:54.570 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: AiClientModel_2
25-08-04.20:56:54.570 [main            ] INFO  AiClientModelNode      - 成功注册模型客户端: AiClientModel_2 -> ollama (openai)
25-08-04.20:56:54.570 [main            ] INFO  AiClientModelNode      - 模型客户端构建完成
25-08-04.20:56:54.570 [main            ] INFO  DefaultArmoryStrategyFactory - === 第5阶段：AI客户端组装 ===
25-08-04.20:56:54.570 [main            ] INFO  AiClientNode           - 开始构建AI客户端，数量: 2
25-08-04.20:56:54.570 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientModel_2'
25-08-04.20:56:54.571 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientAdvisor_1'
25-08-04.20:56:57.014 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@348ced48
25-08-04.20:56:57.049 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=9, active=0, idle=9, waiting=0)
25-08-04.20:56:57.177 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: ChatClient_2
25-08-04.20:56:57.177 [main            ] INFO  AiClientNode           - 成功注册AI客户端: ChatClient_2 -> clientId=2
25-08-04.20:56:57.177 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientModel_1'
25-08-04.20:56:57.177 [main            ] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'AiClientAdvisor_2'
25-08-04.20:56:58.254 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3e0ffa57
25-08-04.20:56:58.293 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=10, active=0, idle=10, waiting=0)
25-08-04.20:56:58.430 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@4e50c11
25-08-04.20:56:58.451 [main            ] INFO  AbstractArmorySupport  - 成功注册Bean: ChatClient_1
25-08-04.20:56:58.451 [main            ] INFO  AiClientNode           - 成功注册AI客户端: ChatClient_1 -> clientId=1
25-08-04.20:56:58.451 [main            ] INFO  AiClientNode           - AI客户端构建完成
25-08-04.20:56:58.451 [main            ] INFO  DefaultArmoryStrategyFactory - AI客户端组装流程完成
25-08-04.20:56:58.451 [main            ] INFO  AiClientInitializer    - AI客户端自动初始化完成
25-08-04.20:56:58.464 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=11, active=0, idle=11, waiting=0)
25-08-04.20:56:58.575 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3cf1b6bc
25-08-04.20:56:58.618 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=12, active=0, idle=12, waiting=0)
25-08-04.20:56:58.752 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@70728e6d
25-08-04.20:56:58.792 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=13, active=0, idle=13, waiting=0)
25-08-04.20:56:58.915 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@37f4ab57
25-08-04.20:56:58.947 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=14, active=0, idle=14, waiting=0)
25-08-04.20:56:59.070 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - Added connection org.mariadb.jdbc.Connection@3da33e0b
25-08-04.20:56:59.103 [MariaDB_HikariCP connection adder] DEBUG HikariPool             - MariaDB_HikariCP - After adding stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:57:20.350 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:57:20.350 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:57:20.350 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:57:50.354 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:57:50.354 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:57:50.354 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:58:18.300 [http-nio-8091-exec-1] INFO  [/]                    - Initializing Spring DispatcherServlet 'dispatcherServlet'
25-08-04.20:58:18.300 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Initializing Servlet 'dispatcherServlet'
25-08-04.20:58:18.301 [http-nio-8091-exec-1] INFO  DispatcherServlet      - Completed initialization in 1 ms
25-08-04.20:58:18.338 [http-nio-8091-exec-1] INFO  AiClientController     - 获取AI客户端列表成功，数量: 2
25-08-04.20:58:20.363 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:58:20.363 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:58:20.363 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:58:29.249 [HttpClient-8-Worker-2] ERROR HttpClientSseClientTransport - SSE connection error
java.io.IOException: http1_0 content, bytes received: 21800
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:979)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:934)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:934)
25-08-04.20:58:29.251 [ForkJoinPool.commonPool-worker-2] ERROR HttpClientSseClientTransport - SSE connection error
java.util.concurrent.CompletionException: java.io.IOException: http1_0 content, bytes received: 21800
	at java.base/java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:332)
	at java.base/java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:347)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:874)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.ResponseSubscribers.lambda$getBodyAsync$2(ResponseSubscribers.java:1152)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.loop(LineSubscriberAdapter.java:410)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter$LineSubscription.signalError(LineSubscriberAdapter.java:199)
	at java.net.http/jdk.internal.net.http.LineSubscriberAdapter.onError(LineSubscriberAdapter.java:105)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.propagateError(Http1Response.java:327)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.complete(Http1Response.java:356)
	at java.net.http/jdk.internal.net.http.Http1Response$Http1BodySubscriber.onError(Http1Response.java:386)
	at java.net.http/jdk.internal.net.http.Http1Response.lambda$readBody$2(Http1Response.java:468)
	at java.base/java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)
	at java.base/java.util.concurrent.CompletableFuture$UniWhenComplete.tryFire(CompletableFuture.java:841)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:2162)
	at java.net.http/jdk.internal.net.http.Http1Response.onReadError(Http1Response.java:554)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:761)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.checkForErrors(Http1AsyncReceiver.java:302)
	at java.net.http/jdk.internal.net.http.Http1AsyncReceiver.flush(Http1AsyncReceiver.java:268)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$LockingRestartableTask.run(SequentialScheduler.java:205)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$CompleteRestartableTask.run(SequentialScheduler.java:149)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.io.IOException: http1_0 content, bytes received: 21800
	at java.net.http/jdk.internal.net.http.common.Utils.wrapWithExtraDetail(Utils.java:351)
	at java.net.http/jdk.internal.net.http.Http1Response$BodyReader.onReadError(Http1Response.java:760)
	... 8 common frames omitted
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at java.net.http/jdk.internal.net.http.SocketTube.readAvailable(SocketTube.java:1170)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.read(SocketTube.java:833)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowTask.run(SocketTube.java:181)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler$SchedulableTask.run(SequentialScheduler.java:230)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:303)
	at java.net.http/jdk.internal.net.http.common.SequentialScheduler.runOrSchedule(SequentialScheduler.java:256)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$InternalReadSubscription.signalReadable(SocketTube.java:774)
	at java.net.http/jdk.internal.net.http.SocketTube$InternalReadPublisher$ReadEvent.signalEvent(SocketTube.java:957)
	at java.net.http/jdk.internal.net.http.SocketTube$SocketFlowEvent.handle(SocketTube.java:253)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.handleEvent(HttpClientImpl.java:979)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.lambda$run$3(HttpClientImpl.java:934)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at java.net.http/jdk.internal.net.http.HttpClientImpl$SelectorManager.run(HttpClientImpl.java:934)
25-08-04.20:58:50.376 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:58:50.376 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:58:50.376 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:59:16.789 [http-nio-8091-exec-3] WARN  DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content-Type 'multipart/form-data;boundary=mbNLpeGtK74cN4gMBnlesJFNATwgBHN-JMMZmz;charset=UTF-8' is not supported]
25-08-04.20:59:20.387 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:59:20.387 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:59:20.387 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.20:59:42.993 [http-nio-8091-exec-5] DEBUG DefaultListableBeanFactory - Creating shared instance of singleton bean 'ChatClient_1'
25-08-04.20:59:43.010 [http-nio-8091-exec-5] DEBUG PromptChatMemoryAdvisor - [PromptChatMemoryAdvisor.before] Memory before processing for conversationId=default: []
25-08-04.20:59:50.390 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:59:50.390 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.20:59:50.390 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.21:00:10.335 [SpringApplicationShutdownHook] INFO  GracefulShutdown       - Commencing graceful shutdown. Waiting for active requests to complete
25-08-04.21:00:20.391 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Before cleanup stats (total=15, active=0, idle=15, waiting=0)
25-08-04.21:00:20.391 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - After cleanup  stats (total=15, active=0, idle=15, waiting=0)
25-08-04.21:00:20.391 [MariaDB_HikariCP housekeeper] DEBUG HikariPool             - MariaDB_HikariCP - Fill pool skipped, pool has sufficient level or currently being filled.
25-08-04.21:00:25.219 [http-nio-8091-exec-5] WARN  SpringAiRetryAutoConfiguration - Retry error. Retry count: 1, Exception: I/O error on POST request for "https://api.openai.com/v1/embeddings": null
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "https://api.openai.com/v1/embeddings": null
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.createResourceAccessException(DefaultRestClient.java:692)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:577)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:765)
	at org.springframework.ai.openai.api.OpenAiApi.embeddings(OpenAiApi.java:294)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$1(OpenAiEmbeddingModel.java:168)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$3(OpenAiEmbeddingModel.java:168)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.call(OpenAiEmbeddingModel.java:166)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:67)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:49)
	at org.springframework.ai.vectorstore.mariadb.MariaDBVectorStore.doSimilaritySearch(MariaDBVectorStore.java:352)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.lambda$similaritySearch$7(AbstractObservationVectorStore.java:126)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.similaritySearch(AbstractObservationVectorStore.java:125)
	at org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor.before(QuestionAnswerAdvisor.java:119)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:51)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at cn.iflytek.trigger.http.controller.AiClientController.chat(AiClientController.java:90)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1047)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:230)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:206)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	... 1 common frames omitted
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:195)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:760)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:848)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 10 common frames omitted
25-08-04.21:00:25.231 [http-nio-8091-exec-5] ERROR AiClientController     - AI客户端对话失败: clientId=1
org.springframework.web.client.ResourceAccessException: I/O error on POST request for "https://api.openai.com/v1/embeddings": null
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.createResourceAccessException(DefaultRestClient.java:692)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchangeInternal(DefaultRestClient.java:577)
	at org.springframework.web.client.DefaultRestClient$DefaultRequestBodyUriSpec.exchange(DefaultRestClient.java:535)
	at org.springframework.web.client.RestClient$RequestHeadersSpec.exchange(RestClient.java:677)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.executeAndExtract(DefaultRestClient.java:809)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntityInternal(DefaultRestClient.java:769)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.toEntity(DefaultRestClient.java:765)
	at org.springframework.ai.openai.api.OpenAiApi.embeddings(OpenAiApi.java:294)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$1(OpenAiEmbeddingModel.java:168)
	at org.springframework.retry.support.RetryTemplate.doExecute(RetryTemplate.java:357)
	at org.springframework.retry.support.RetryTemplate.execute(RetryTemplate.java:230)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.lambda$call$3(OpenAiEmbeddingModel.java:168)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.openai.OpenAiEmbeddingModel.call(OpenAiEmbeddingModel.java:166)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:67)
	at org.springframework.ai.embedding.EmbeddingModel.embed(EmbeddingModel.java:49)
	at org.springframework.ai.vectorstore.mariadb.MariaDBVectorStore.doSimilaritySearch(MariaDBVectorStore.java:352)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.lambda$similaritySearch$7(AbstractObservationVectorStore.java:126)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.vectorstore.observation.AbstractObservationVectorStore.similaritySearch(AbstractObservationVectorStore.java:125)
	at org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor.before(QuestionAnswerAdvisor.java:119)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:51)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.advisor.api.BaseAdvisor.adviseCall(BaseAdvisor.java:52)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.lambda$nextCall$1(DefaultAroundAdvisorChain.java:110)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.advisor.DefaultAroundAdvisorChain.nextCall(DefaultAroundAdvisorChain.java:110)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.lambda$doGetObservableChatClientResponse$1(DefaultChatClient.java:469)
	at io.micrometer.observation.Observation.observe(Observation.java:564)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:467)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.doGetObservableChatClientResponse(DefaultChatClient.java:446)
	at org.springframework.ai.chat.client.DefaultChatClient$DefaultCallResponseSpec.content(DefaultChatClient.java:441)
	at cn.iflytek.trigger.http.controller.AiClientController.chat(AiClientController.java:90)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:257)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.net.ConnectException: null
	at java.net.http/jdk.internal.net.http.common.Utils.toConnectException(Utils.java:1047)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:198)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.checkRetryConnect(PlainHttpConnection.java:230)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$1(PlainHttpConnection.java:206)
	at java.base/java.util.concurrent.CompletableFuture.uniHandle(CompletableFuture.java:934)
	at java.base/java.util.concurrent.CompletableFuture$UniHandle.tryFire(CompletableFuture.java:911)
	at java.base/java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:510)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1773)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	... 1 common frames omitted
Caused by: java.nio.channels.ClosedChannelException: null
	at java.base/sun.nio.ch.SocketChannelImpl.ensureOpen(SocketChannelImpl.java:195)
	at java.base/sun.nio.ch.SocketChannelImpl.beginConnect(SocketChannelImpl.java:760)
	at java.base/sun.nio.ch.SocketChannelImpl.connect(SocketChannelImpl.java:848)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.lambda$connectAsync$0(PlainHttpConnection.java:183)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:569)
	at java.net.http/jdk.internal.net.http.PlainHttpConnection.connectAsync(PlainHttpConnection.java:185)
	... 10 common frames omitted
25-08-04.21:00:25.292 [tomcat-shutdown ] INFO  GracefulShutdown       - Graceful shutdown complete
25-08-04.21:00:25.297 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown initiated...
25-08-04.21:00:25.297 [SpringApplicationShutdownHook] DEBUG HikariPool             - MariaDB_HikariCP - Before shutdown stats (total=15, active=0, idle=15, waiting=0)
25-08-04.21:00:25.298 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@42107318: (connection evicted)
25-08-04.21:00:25.308 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@46dedde9: (connection evicted)
25-08-04.21:00:25.323 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@329517d6: (connection evicted)
25-08-04.21:00:25.339 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@31ec20e5: (connection evicted)
25-08-04.21:00:25.355 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@2fdbe14d: (connection evicted)
25-08-04.21:00:25.370 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@2293e88c: (connection evicted)
25-08-04.21:00:25.386 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@daeeb8: (connection evicted)
25-08-04.21:00:25.401 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@4d253e55: (connection evicted)
25-08-04.21:00:25.417 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@348ced48: (connection evicted)
25-08-04.21:00:25.433 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3e0ffa57: (connection evicted)
25-08-04.21:00:25.449 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@4e50c11: (connection evicted)
25-08-04.21:00:25.464 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3cf1b6bc: (connection evicted)
25-08-04.21:00:25.479 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@70728e6d: (connection evicted)
25-08-04.21:00:25.494 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@37f4ab57: (connection evicted)
25-08-04.21:00:25.510 [MariaDB_HikariCP connection closer] DEBUG PoolBase               - MariaDB_HikariCP - Closing connection org.mariadb.jdbc.Connection@3da33e0b: (connection evicted)
25-08-04.21:00:25.526 [SpringApplicationShutdownHook] DEBUG HikariPool             - MariaDB_HikariCP - After shutdown stats (total=0, active=0, idle=0, waiting=0)
25-08-04.21:00:25.526 [SpringApplicationShutdownHook] INFO  HikariDataSource       - MariaDB_HikariCP - Shutdown completed.
25-08-04.21:00:25.526 [SpringApplicationShutdownHook] DEBUG DisposableBeanAdapter  - Custom destroy method 'shutdown' on bean with name 'threadPoolExecutor' completed
