25-08-03.22:15:32.689 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.22:15:32.689 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.22:15:32.689 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiC<PERSON>Mapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.22:15:32.689 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. <PERSON> already defined with the same name!
25-08-03.22:15:32.690 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:15:32.690 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:15:32.690 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:15:32.690 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:15:32.690 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.22:54:34.083 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.084 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:54:34.085 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.22:54:37.561 [main            ] ERROR RootNode               - 数据加载失败
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:54:37.569 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 数据加载失败
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:44)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	... 22 common frames omitted
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:54:37.575 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 数据加载失败
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:44)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	... 21 common frames omitted
Caused by: java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	... 22 common frames omitted
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:54:37.627 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@46702a7d]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:57:15.968 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.22:57:19.266 [main            ] ERROR RootNode               - 数据加载失败
java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:57:19.278 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 数据加载失败
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:44)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	... 22 common frames omitted
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:57:19.283 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 数据加载失败
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:44)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:43)
	... 21 common frames omitted
Caused by: java.util.concurrent.ExecutionException: java.lang.RuntimeException: Failed to deserialize transport config
	at java.base/java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:396)
	at java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2096)
	at cn.iflytek.domain.agent.service.node.RootNode.multiThreadLoadData(RootNode.java:77)
	at cn.iflytek.domain.agent.service.node.RootNode.doApply(RootNode.java:41)
	... 22 common frames omitted
Caused by: java.lang.RuntimeException: Failed to deserialize transport config
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:138)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.findAll(AiClientToolMcpRepositoryImpl.java:68)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl$$SpringCGLIB$$0.findAll(<generated>)
	at cn.iflytek.domain.agent.service.node.RootNode.lambda$multiThreadLoadData$4(RootNode.java:69)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run$$$capture(CompletableFuture.java:1768)
	at java.base/java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: com.fasterxml.jackson.databind.JsonMappingException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78] (through reference chain: cn.iflytek.domain.agent.model.AiClientToolMcp$TransportConfigSse["baseUri"])
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:401)
	at com.fasterxml.jackson.databind.JsonMappingException.wrapWithPath(JsonMappingException.java:360)
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.wrapAndThrow(BeanDeserializerBase.java:1964)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:312)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:177)
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342)
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4917)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3860)
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3828)
	at cn.iflytek.infrastructure.adapter.repository.AiClientToolMcpRepositoryImpl.convertToAiClientToolMcp(AiClientToolMcpRepositoryImpl.java:120)
	... 25 common frames omitted
Caused by: com.fasterxml.jackson.core.JsonParseException: Illegal unquoted character ((CTRL-CHAR, code 13)): has to be escaped using backslash to be included in string value
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 2, column: 78]
	at com.fasterxml.jackson.core.JsonParser._constructReadException(JsonParser.java:2660)
	at com.fasterxml.jackson.core.base.ParserBase._throwUnquotedSpace(ParserBase.java:1409)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString2(ReaderBasedJsonParser.java:2202)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser._finishString(ReaderBasedJsonParser.java:2173)
	at com.fasterxml.jackson.core.json.ReaderBasedJsonParser.getText(ReaderBasedJsonParser.java:295)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:42)
	at com.fasterxml.jackson.databind.deser.std.StringDeserializer.deserialize(StringDeserializer.java:11)
	at com.fasterxml.jackson.databind.deser.impl.MethodProperty.deserializeAndSet(MethodProperty.java:129)
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:310)
	... 31 common frames omitted
25-08-03.22:57:19.339 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@2c016c9]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.751 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.22:59:21.752 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.22:59:37.229 [main            ] ERROR AiClientAdvisorNode    - 构建顾问失败: advisorId=3, advisorName=日志记录顾问, advisorType=SimpleLoggerAdvisor
java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
25-08-03.22:59:37.232 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 构建顾问失败: 日志记录顾问
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:39)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	... 22 common frames omitted
25-08-03.22:59:37.233 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 构建顾问失败: 日志记录顾问
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:39)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	... 21 common frames omitted
Caused by: java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	... 22 common frames omitted
25-08-03.22:59:37.372 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@1e729758]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.995 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:09:25.996 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.23:09:30.803 [main            ] ERROR AiClientAdvisorNode    - 构建顾问失败: advisorId=3, advisorName=日志记录顾问, advisorType=SimpleLoggerAdvisor
java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
25-08-03.23:09:30.805 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 构建顾问失败: 日志记录顾问
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:39)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	... 22 common frames omitted
25-08-03.23:09:30.805 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 构建顾问失败: 日志记录顾问
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:39)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:51)
	... 21 common frames omitted
Caused by: java.lang.RuntimeException: err! advisorType SimpleLoggerAdvisor not exist!
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.createAdvisor(AiClientAdvisorNode.java:76)
	at cn.iflytek.domain.agent.service.node.AiClientAdvisorNode.doApply(AiClientAdvisorNode.java:32)
	... 22 common frames omitted
25-08-03.23:09:30.824 [main            ] ERROR SpringApplication      - Application run failed
java.lang.IllegalStateException: argument type mismatch
HandlerMethod details: 
Bean [cn.iflytek.config.DataSourceConfig$$SpringCGLIB$$0]
Method [public void cn.iflytek.config.DataSourceConfig.checkDataSourceHealth(javax.sql.DataSource)]
Resolved arguments: 
[0] [type=org.springframework.boot.context.event.ApplicationReadyEvent] [value=org.springframework.boot.context.event.ApplicationReadyEvent[source=org.springframework.boot.SpringApplication@2bbc2154]]

	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:386)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.processEvent(ApplicationListenerMethodAdapter.java:254)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.onApplicationEvent(ApplicationListenerMethodAdapter.java:173)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.doInvokeListener(SimpleApplicationEventMulticaster.java:185)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.invokeListener(SimpleApplicationEventMulticaster.java:178)
	at org.springframework.context.event.SimpleApplicationEventMulticaster.multicastEvent(SimpleApplicationEventMulticaster.java:156)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:454)
	at org.springframework.context.support.AbstractApplicationContext.publishEvent(AbstractApplicationContext.java:387)
	at org.springframework.boot.context.event.EventPublishingRunListener.ready(EventPublishingRunListener.java:109)
	at org.springframework.boot.SpringApplicationRunListeners.lambda$ready$6(SpringApplicationRunListeners.java:80)
	at java.base/java.lang.Iterable.forEach(Iterable.java:75)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:118)
	at org.springframework.boot.SpringApplicationRunListeners.doWithListeners(SpringApplicationRunListeners.java:112)
	at org.springframework.boot.SpringApplicationRunListeners.ready(SpringApplicationRunListeners.java:80)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:332)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.context.event.ApplicationListenerMethodAdapter.doInvoke(ApplicationListenerMethodAdapter.java:382)
	... 17 common frames omitted
25-08-03.23:10:53.773 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.773 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientAdvisorRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.773 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMapper' and 'cn.iflytek.infrastructure.dao.AiClientMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientMcpRelMapper' and 'cn.iflytek.infrastructure.dao.AiClientMcpRelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientModelToolConfigMapper' and 'cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientSystemPromptMapper' and 'cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - Skipping MapperFactoryBean with name 'aiClientToolMcpMapper' and 'cn.iflytek.infrastructure.dao.AiClientToolMcpMapper' mapperInterface. Bean already defined with the same name!
25-08-03.23:10:53.774 [main            ] WARN  ClassPathMapperScanner - No MyBatis mapper was found in '[cn.iflytek.infrastructure.dao]' package. Please check your configuration.
25-08-03.23:11:02.835 [main            ] ERROR AiClientNode           - 构建AI客户端失败: clientId=3
org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'AiClientModel_1': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:294)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1272)
	at cn.iflytek.domain.agent.service.node.AbstractArmorySupport.getBean(AbstractArmorySupport.java:54)
	at cn.iflytek.domain.agent.service.node.AiClientNode.doApply(AiClientNode.java:43)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:59)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
25-08-03.23:11:02.837 [main            ] ERROR DefaultArmoryStrategyFactory - AI客户端组装流程失败
java.lang.RuntimeException: 构建AI客户端失败: clientId=3
	at cn.iflytek.domain.agent.service.node.AiClientNode.doApply(AiClientNode.java:93)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:59)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'AiClientModel_1': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:294)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1272)
	at cn.iflytek.domain.agent.service.node.AbstractArmorySupport.getBean(AbstractArmorySupport.java:54)
	at cn.iflytek.domain.agent.service.node.AiClientNode.doApply(AiClientNode.java:43)
	... 22 common frames omitted
25-08-03.23:11:02.837 [main            ] ERROR AiClientInitializer    - AI客户端自动初始化失败，请检查配置
java.lang.RuntimeException: AI客户端组装流程失败
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:65)
	at cn.iflytek.config.AiClientInitializer.run(AiClientInitializer.java:27)
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784)
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82)
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60)
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784)
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183)
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150)
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350)
	at cn.iflytek.Application.main(Application.java:14)
Caused by: java.lang.RuntimeException: 构建AI客户端失败: clientId=3
	at cn.iflytek.domain.agent.service.node.AiClientNode.doApply(AiClientNode.java:93)
	at cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory.assembleAiClients(DefaultArmoryStrategyFactory.java:59)
	... 21 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationNotAllowedException: Error creating bean with name 'AiClientModel_1': Singleton bean creation not allowed while singletons of this factory are in destruction (Do not request a bean from a BeanFactory in a destroy method implementation!)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:294)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.context.support.AbstractApplicationContext.getBean(AbstractApplicationContext.java:1272)
	at cn.iflytek.domain.agent.service.node.AbstractArmorySupport.getBean(AbstractArmorySupport.java:54)
	at cn.iflytek.domain.agent.service.node.AiClientNode.doApply(AiClientNode.java:43)
	... 22 common frames omitted
