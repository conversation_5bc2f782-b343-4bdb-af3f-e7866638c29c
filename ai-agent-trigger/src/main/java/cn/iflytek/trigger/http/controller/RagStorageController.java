package cn.iflytek.trigger.http.controller;

import cn.iflytek.domain.agent.service.rag.service.IRagStorageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RAG存储管理控制器
 * 提供文档上传和删除功能，使用Spring AI ETL Pipeline
 */
@Slf4j
@RestController
@RequestMapping("/api/rag")
public class RagStorageController {

    @Resource
    private IRagStorageService ragStorageService;

    /**
     * 上传文档文件到知识库
     * 使用Spring AI ETL Pipeline处理文档
     */
    @PostMapping("/documents/upload")
    public Map<String, Object> uploadDocument(@RequestParam String ragName,
                                            @RequestParam("file") MultipartFile file) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("上传文档请求: 知识库={}, 文件={}", ragName, file.getOriginalFilename());

            if (ragName == null || ragName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "知识库名称不能为空");
                return result;
            }

            if (file.isEmpty()) {
                result.put("success", false);
                result.put("message", "上传文件不能为空");
                return result;
            }

            // 将MultipartFile转换为Resource
            ByteArrayResource resource = new ByteArrayResource(file.getBytes()) {
                @Override
                public String getFilename() {
                    return file.getOriginalFilename();
                }
            };

            boolean success = ragStorageService.uploadDocument(ragName, resource);
            result.put("success", success);
            result.put("message", success ? "文档上传成功" : "文档上传失败");
            result.put("ragName", ragName);
            result.put("filename", file.getOriginalFilename());

        } catch (Exception e) {
            log.error("上传文档失败: 知识库={}, 文件={}", ragName, file.getOriginalFilename(), e);
            result.put("success", false);
            result.put("message", "上传文档失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 上传多个文档到知识库
     * 接收Document对象列表
     */
    @PostMapping("/documents/batch-upload")
    public Map<String, Object> uploadDocuments(@RequestParam String ragName,
                                             @RequestBody List<Map<String, Object>> documentData) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("批量上传文档到知识库: {}, 文档数量: {}", ragName, documentData.size());

            if (ragName == null || ragName.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "知识库名称不能为空");
                return result;
            }

            if (documentData.isEmpty()) {
                result.put("success", false);
                result.put("message", "文档列表不能为空");
                return result;
            }

            // 转换为Document对象
            List<Document> documents = documentData.stream()
                .map(data -> {
                    String id = (String) data.get("id");
                    String content = (String) data.get("content");
                    @SuppressWarnings("unchecked")
                    Map<String, Object> metadata = (Map<String, Object>) data.getOrDefault("metadata", new HashMap<>());

                    return new Document(id, content, metadata);
                })
                .toList();

            boolean success = ragStorageService.uploadDocuments(ragName, documents);
            result.put("success", success);
            result.put("message", success ? "文档批量上传成功" : "文档批量上传失败");
            result.put("ragName", ragName);
            result.put("documentCount", documents.size());

        } catch (Exception e) {
            log.error("批量上传文档失败: 知识库={}", ragName, e);
            result.put("success", false);
            result.put("message", "批量上传文档失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 删除知识库的所有文档
     */
    @DeleteMapping("/documents/{ragName}")
    public Map<String, Object> deleteDocuments(@PathVariable String ragName) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("删除知识库文档请求: {}", ragName);

            boolean success = ragStorageService.deleteDocuments(ragName);
            result.put("success", success);
            result.put("message", success ? "知识库文档删除成功" : "知识库文档删除失败");
            result.put("ragName", ragName);

        } catch (Exception e) {
            log.error("删除知识库文档失败: {}", ragName, e);
            result.put("success", false);
            result.put("message", "删除知识库文档失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 根据文档ID删除指定文档
     */
    @DeleteMapping("/documents/by-ids")
    public Map<String, Object> deleteDocumentsByIds(@RequestBody List<String> documentIds) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("删除指定文档请求: 文档数量={}", documentIds.size());

            if (documentIds.isEmpty()) {
                result.put("success", false);
                result.put("message", "文档ID列表不能为空");
                return result;
            }

            boolean success = ragStorageService.deleteDocumentsByIds(documentIds);
            result.put("success", success);
            result.put("message", success ? "文档删除成功" : "文档删除失败");
            result.put("deletedCount", documentIds.size());

        } catch (Exception e) {
            log.error("删除指定文档失败: 文档数量={}", documentIds.size(), e);
            result.put("success", false);
            result.put("message", "删除指定文档失败: " + e.getMessage());
        }

        return result;
    }

}
