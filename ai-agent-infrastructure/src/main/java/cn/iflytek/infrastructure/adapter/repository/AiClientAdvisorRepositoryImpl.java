package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientAdvisorRepository;
import cn.iflytek.domain.agent.model.AiClientAdvisor;
import cn.iflytek.infrastructure.dao.AiClientAdvisorMapper;
import cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端顾问配置仓储实现
 */
@Repository
public class AiClientAdvisorRepositoryImpl implements AiClientAdvisorRepository {

    @Resource
    private AiClientAdvisorMapper advisorMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Long save(AiClientAdvisor advisor) {
        AiClientAdvisorPO advisorPO = convertToAiClientAdvisorPO(advisor);
        advisorMapper.insert(advisorPO);
        return advisorPO.getId();
    }

    @Override
    public AiClientAdvisor findById(Long id) {
        AiClientAdvisorPO advisorPO = advisorMapper.selectById(id);
        if (advisorPO == null) {
            return null;
        }

        return convertToAiClientAdvisor(advisorPO);
    }

    @Override
    public AiClientAdvisor findByAdvisorName(String advisorName) {
        AiClientAdvisorPO advisorPO = advisorMapper.selectByAdvisorName(advisorName);
        if (advisorPO == null) {
            return null;
        }

        return convertToAiClientAdvisor(advisorPO);
    }

    @Override
    public List<AiClientAdvisor> findByAdvisorType(String advisorType) {
        List<AiClientAdvisorPO> advisorPOList = advisorMapper.selectByAdvisorType(advisorType);
        return advisorPOList.stream()
                .map(this::convertToAiClientAdvisor)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClientAdvisor> findAll() {
        List<AiClientAdvisorPO> advisorPOList = advisorMapper.selectAll();
        return advisorPOList.stream()
                .map(this::convertToAiClientAdvisor)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(AiClientAdvisor advisor) {
        AiClientAdvisorPO advisorPO = convertToAiClientAdvisorPO(advisor);
        int result = advisorMapper.updateById(advisorPO);
        return result > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        int result = advisorMapper.deleteById(id);
        return result > 0;
    }

    private AiClientAdvisorPO convertToAiClientAdvisorPO(AiClientAdvisor advisor) {
        AiClientAdvisorPO.AiClientAdvisorPOBuilder builder = AiClientAdvisorPO.builder()
                .id(advisor.getId())
                .advisorName(advisor.getAdvisorName())
                .advisorType(advisor.getAdvisorType())
                .orderNum(advisor.getOrderNum());

        // 根据顾问类型序列化相应的配置到extParam字段
        try {
            if ("ChatMemory".equals(advisor.getAdvisorType()) && advisor.getChatMemory() != null) {
                String chatMemoryJson = objectMapper.writeValueAsString(advisor.getChatMemory());
                builder.extParam(chatMemoryJson);
            } else if ("RagAnswer".equals(advisor.getAdvisorType()) && advisor.getRagAnswer() != null) {
                String ragAnswerJson = objectMapper.writeValueAsString(advisor.getRagAnswer());
                builder.extParam(ragAnswerJson);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize advisor config", e);
        }

        return builder.build();
    }

    private AiClientAdvisor convertToAiClientAdvisor(AiClientAdvisorPO advisorPO) {
        AiClientAdvisor.AiClientAdvisorBuilder builder = AiClientAdvisor.builder()
                .id(advisorPO.getId())
                .advisorName(advisorPO.getAdvisorName())
                .advisorType(advisorPO.getAdvisorType())
                .orderNum(advisorPO.getOrderNum());

        // 根据顾问类型反序列化extParam字段到相应的配置对象
        if (advisorPO.getExtParam() != null && !advisorPO.getExtParam().trim().isEmpty()) {
            try {
                if ("ChatMemory".equals(advisorPO.getAdvisorType())) {
                    AiClientAdvisor.ChatMemory chatMemory = objectMapper.readValue(
                            advisorPO.getExtParam(), AiClientAdvisor.ChatMemory.class);
                    builder.chatMemory(chatMemory);
                } else if ("RagAnswer".equals(advisorPO.getAdvisorType())) {
                    AiClientAdvisor.RagAnswer ragAnswer = objectMapper.readValue(
                            advisorPO.getExtParam(), AiClientAdvisor.RagAnswer.class);
                    builder.ragAnswer(ragAnswer);
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to deserialize advisor config", e);
            }
        }

        return builder.build();
    }
}
