package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientToolMcpRepository;
import cn.iflytek.domain.agent.model.AiClientToolMcp;
import cn.iflytek.infrastructure.dao.AiClientToolMcpMapper;
import cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Repository;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AI客户端MCP工具配置仓储实现
 */
@Repository
public class AiClientToolMcpRepositoryImpl implements AiClientToolMcpRepository {

    @Resource
    private AiClientToolMcpMapper mcpMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Long save(AiClientToolMcp mcp) {
        AiClientToolMcpPO mcpPO = convertToAiClientToolMcpPO(mcp);
        mcpMapper.insert(mcpPO);
        return mcpPO.getId();
    }

    @Override
    public AiClientToolMcp findById(Long id) {
        AiClientToolMcpPO mcpPO = mcpMapper.selectById(id);
        if (mcpPO == null) {
            return null;
        }

        return convertToAiClientToolMcp(mcpPO);
    }

    @Override
    public AiClientToolMcp findByMcpName(String mcpName) {
        AiClientToolMcpPO mcpPO = mcpMapper.selectByMcpName(mcpName);
        if (mcpPO == null) {
            return null;
        }

        return convertToAiClientToolMcp(mcpPO);
    }

    @Override
    public List<AiClientToolMcp> findByTransportType(String transportType) {
        List<AiClientToolMcpPO> mcpPOList = mcpMapper.selectByTransportType(transportType);
        return mcpPOList.stream()
                .map(this::convertToAiClientToolMcp)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClientToolMcp> findAll() {
        List<AiClientToolMcpPO> mcpPOList = mcpMapper.selectAll();
        return mcpPOList.stream()
                .map(this::convertToAiClientToolMcp)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(AiClientToolMcp mcp) {
        AiClientToolMcpPO mcpPO = convertToAiClientToolMcpPO(mcp);
        int result = mcpMapper.updateById(mcpPO);
        return result > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        int result = mcpMapper.deleteById(id);
        return result > 0;
    }

    private AiClientToolMcpPO convertToAiClientToolMcpPO(AiClientToolMcp mcp) {
        AiClientToolMcpPO.AiClientToolMcpPOBuilder builder = AiClientToolMcpPO.builder()
                .id(mcp.getId())
                .mcpName(mcp.getMcpName())
                .transportType(mcp.getTransportType())
                .requestTimeout(mcp.getRequestTimeout());

        // 根据传输类型序列化相应的配置到transportConfig字段
        try {
            if ("sse".equals(mcp.getTransportType()) && mcp.getTransportConfigSse() != null) {
                String sseConfigJson = objectMapper.writeValueAsString(mcp.getTransportConfigSse());
                builder.transportConfig(sseConfigJson);
            } else if ("stdio".equals(mcp.getTransportType()) && mcp.getTransportConfigStdio() != null) {
                // 将 TransportConfigStdio 的 stdio Map 直接序列化，而不是整个对象
                // 这样数据库中存储的就是 {"g-search": {"command": "npx", "args": ["-y", "g-search-mcp"]}} 格式
                String stdioConfigJson = objectMapper.writeValueAsString(mcp.getTransportConfigStdio().getStdio());
                builder.transportConfig(stdioConfigJson);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize transport config", e);
        }

        return builder.build();
    }

    private AiClientToolMcp convertToAiClientToolMcp(AiClientToolMcpPO mcpPO) {
        AiClientToolMcp.AiClientToolMcpBuilder builder = AiClientToolMcp.builder()
                .id(mcpPO.getId())
                .mcpName(mcpPO.getMcpName())
                .transportType(mcpPO.getTransportType())
                .requestTimeout(mcpPO.getRequestTimeout());

        // 根据传输类型反序列化transportConfig字段到相应的配置对象
        if (mcpPO.getTransportConfig() != null && !mcpPO.getTransportConfig().trim().isEmpty()) {
            try {
                if ("sse".equals(mcpPO.getTransportType())) {
                    AiClientToolMcp.TransportConfigSse sseConfig = objectMapper.readValue(
                            mcpPO.getTransportConfig(), AiClientToolMcp.TransportConfigSse.class);
                    builder.transportConfigSse(sseConfig);
                } else if ("stdio".equals(mcpPO.getTransportType())) {
                    // 从数据库读取的是 {"g-search": {"command": "npx", "args": ["-y", "g-search-mcp"]}} 格式
                    // 需要将其包装成 TransportConfigStdio 对象的 stdio 字段
                    TypeReference<Map<String, AiClientToolMcp.TransportConfigStdio.Stdio>> typeRef =
                            new TypeReference<>() {
                            };
                    Map<String, AiClientToolMcp.TransportConfigStdio.Stdio> stdioMap = objectMapper.readValue(
                            mcpPO.getTransportConfig(), typeRef);

                    AiClientToolMcp.TransportConfigStdio stdioConfig = AiClientToolMcp.TransportConfigStdio.builder()
                            .stdio(stdioMap)
                            .build();
                    builder.transportConfigStdio(stdioConfig);
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to deserialize transport config", e);
            }
        }

        return builder.build();
    }
}
