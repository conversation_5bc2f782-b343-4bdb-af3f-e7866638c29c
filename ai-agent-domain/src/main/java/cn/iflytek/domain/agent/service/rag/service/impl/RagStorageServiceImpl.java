package cn.iflytek.domain.agent.service.rag.service.impl;

import cn.iflytek.domain.agent.service.rag.service.IRagStorageService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.ExtractedTextFormatter;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.pdf.config.PdfDocumentReaderConfig;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RAG存储服务实现
 * 使用Spring AI ETL Pipeline处理文档上传和删除
 */
@Slf4j
@Service
public class RagStorageServiceImpl implements IRagStorageService {

    @Resource
    private VectorStore vectorStore;

    private final TokenTextSplitter textSplitter;

    public RagStorageServiceImpl() {
        // 初始化文本分割器，设置合适的参数
        this.textSplitter = new TokenTextSplitter(800, 350, 5, 10000, true);
    }

    @Override
    public boolean uploadDocument(String ragName, Resource resource) {
        try {
            log.info("上传文档到知识库: {}, 资源: {}", ragName, resource.getFilename());

            // 使用Spring AI ETL Pipeline处理文档
            List<Document> documents = readDocument(resource);

            // 使用文本分割器处理文档
            List<Document> splitDocuments = textSplitter.apply(documents);

            // 为文档添加知识库标识
            List<Document> enrichedDocuments = enrichDocuments(splitDocuments, ragName);

            // 写入向量存储
            vectorStore.add(enrichedDocuments);

            log.info("文档上传成功: 知识库={}, 文档数量={}", ragName, enrichedDocuments.size());
            return true;
        } catch (Exception e) {
            log.error("上传文档失败: 知识库={}, 资源={}", ragName, resource.getFilename(), e);
            return false;
        }
    }

    @Override
    public boolean uploadDocuments(String ragName, List<Document> documents) {
        try {
            log.info("上传文档到知识库: {}, 文档数量: {}", ragName, documents.size());

            // 使用文本分割器处理文档
            List<Document> splitDocuments = textSplitter.apply(documents);

            // 为文档添加知识库标识
            List<Document> enrichedDocuments = enrichDocuments(splitDocuments, ragName);

            // 写入向量存储
            vectorStore.add(enrichedDocuments);

            log.info("文档上传成功: 知识库={}, 处理后文档数量={}", ragName, enrichedDocuments.size());
            return true;
        } catch (Exception e) {
            log.error("上传文档失败: 知识库={}, 文档数量={}", ragName, documents.size(), e);
            return false;
        }
    }

    @Override
    public boolean deleteDocuments(String ragName) {
        try {
            log.info("删除知识库文档: {}", ragName);

            // 通过搜索获取所有相关文档并删除
            List<Document> documents = vectorStore.similaritySearch(
                SearchRequest.builder()
                    .query("*")
                    .topK(10000)
                    .build()
            );

            // 过滤出属于指定知识库的文档
            List<String> documentIds = documents.stream()
                .filter(doc -> ragName.equals(doc.getMetadata().get("rag_name")))
                .map(Document::getId)
                .collect(Collectors.toList());

            if (!documentIds.isEmpty()) {
                vectorStore.delete(documentIds);
            }

            log.info("知识库文档删除成功: {}, 删除文档数量: {}", ragName, documentIds.size());
            return true;
        } catch (Exception e) {
            log.error("删除知识库文档失败: {}", ragName, e);
            return false;
        }
    }

    @Override
    public boolean deleteDocumentsByIds(List<String> documentIds) {
        try {
            log.info("删除指定文档: 文档数量={}", documentIds.size());

            if (documentIds.isEmpty()) {
                log.warn("文档ID列表为空，无需删除");
                return true;
            }

            vectorStore.delete(documentIds);
            log.info("文档删除成功: 删除文档数量={}", documentIds.size());
            return true;
        } catch (Exception e) {
            log.error("删除文档失败: 文档数量={}", documentIds.size(), e);
            return false;
        }
    }

    /**
     * 读取文档，根据文件类型选择合适的DocumentReader
     */
    private List<Document> readDocument(Resource resource) {
        try {
            String filename = resource.getFilename();
            if (filename == null) {
                throw new IllegalArgumentException("无法获取文件名");
            }

            String extension = getFileExtension(filename).toLowerCase();

            return switch (extension) {
                case "pdf" -> {
                    PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(resource,
                        PdfDocumentReaderConfig.builder()
                            .withPageTopMargin(0)
                            .withPageExtractedTextFormatter(ExtractedTextFormatter.builder()
                                .withNumberOfTopTextLinesToDelete(0)
                                .build())
                            .withPagesPerDocument(1)
                            .build());
                    yield pdfReader.get();
                }
                case "doc", "docx", "ppt", "pptx", "html", "htm" -> {
                    TikaDocumentReader tikaReader = new TikaDocumentReader(resource);
                    yield tikaReader.get();
                }
                default -> throw new IllegalArgumentException("不支持的文件类型: " + extension);
            };
        } catch (Exception e) {
            log.error("读取文档失败: {}", resource.getFilename(), e);
            throw new RuntimeException("读取文档失败", e);
        }
    }

    /**
     * 为文档添加知识库标识和其他元数据
     */
    private List<Document> enrichDocuments(List<Document> documents, String ragName) {
        return documents.stream()
            .map(doc -> {
                Map<String, Object> metadata = new HashMap<>(doc.getMetadata());
                metadata.put("rag_name", ragName);
                metadata.put("type", "document");
                metadata.put("added_at", System.currentTimeMillis());

                return new Document(doc.getId(), doc.getContent(), metadata);
            })
            .collect(Collectors.toList());
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }
}
