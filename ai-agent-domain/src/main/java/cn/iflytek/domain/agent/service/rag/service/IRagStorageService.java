package cn.iflytek.domain.agent.service.rag.service;

import org.springframework.ai.document.Document;
import org.springframework.core.io.Resource;

import java.util.List;

/**
 * RAG存储服务接口
 * 提供文档上传和删除功能，使用Spring AI ETL Pipeline
 */
public interface IRagStorageService {

    /**
     * 上传文档到向量存储
     * 使用Spring AI ETL Pipeline处理文档
     *
     * @param ragName 知识库名称，用于文档标识
     * @param resource 文档资源
     * @return 上传成功返回true，失败返回false
     */
    boolean uploadDocument(String ragName, Resource resource);

    /**
     * 上传多个文档到向量存储
     * 使用Spring AI ETL Pipeline处理文档
     *
     * @param ragName 知识库名称，用于文档标识
     * @param documents 要上传的文档列表
     * @return 上传成功返回true，失败返回false
     */
    boolean uploadDocuments(String ragName, List<Document> documents);

    /**
     * 删除指定知识库的所有文档
     *
     * @param ragName 知识库名称
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteDocuments(String ragName);

    /**
     * 删除指定的文档
     *
     * @param documentIds 要删除的文档ID列表
     * @return 删除成功返回true，失败返回false
     */
    boolean deleteDocumentsByIds(List<String> documentIds);
}
