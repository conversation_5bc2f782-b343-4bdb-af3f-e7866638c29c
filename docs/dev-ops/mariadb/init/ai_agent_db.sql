/*
 Navicat Premium Data Transfer

 Source Server         : ali_mariadb
 Source Server Type    : MariaDB
 Source Server Version : 110802
 Source Host           : ************:3306
 Source Schema         : ai_agent_db

 Target Server Type    : MariaDB
 Target Server Version : 110802
 File Encoding         : 65001

 Date: 03/08/2025 22:00:49
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for ai_client
-- ----------------------------
DROP TABLE IF EXISTS `ai_client`;
CREATE TABLE `ai_client`  (
  `client_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客户端ID',
  `system_prompt_id` bigint(20) NULL DEFAULT NULL COMMENT '系统提示词ID',
  `model_bean_id` bigint(20) NOT NULL COMMENT '模型Bean ID',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`client_id`) USING BTREE,
  INDEX `idx_system_prompt_id`(`system_prompt_id`) USING BTREE,
  INDEX `idx_model_bean_id`(`model_bean_id`) USING BTREE,
  CONSTRAINT `fk_ai_client_model` FOREIGN KEY (`model_bean_id`) REFERENCES `ai_client_model` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_ai_client_system_prompt` FOREIGN KEY (`system_prompt_id`) REFERENCES `ai_client_system_prompt` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client
-- ----------------------------
INSERT INTO `ai_client` VALUES (1, 1, 1, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client` VALUES (2, 2, 3, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client` VALUES (3, 3, 1, '2025-08-03 07:38:27', '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for ai_client_advisor
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_advisor`;
CREATE TABLE `ai_client_advisor`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `advisor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '顾问名称',
  `advisor_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '顾问类型(PromptChatMemory/RagAnswer/SimpleLoggerAdvisor等)',
  `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '顺序号',
  `ext_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '格外参数配置',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_advisor_name`(`advisor_name`) USING BTREE,
  INDEX `idx_advisor_type`(`advisor_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端顾问配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_advisor
-- ----------------------------
INSERT INTO `ai_client_advisor` VALUES (1, '记忆管理顾问', 'ChatMemory', 1, '{\n    \"maxMessages\": 200\n}', '2025-08-03 07:38:27', '2025-08-03 13:46:43');
INSERT INTO `ai_client_advisor` VALUES (2, 'RAG问答顾问', 'RagAnswer', 2, '{\n    \"topK\": \"4\",\n    \"filterExpression\": \"knowledge == \'知识库名称\'\"\n}', '2025-08-03 07:38:27', '2025-08-03 13:42:52');
INSERT INTO `ai_client_advisor` VALUES (3, '日志记录顾问', 'SimpleLoggerAdvisor', 3, NULL, '2025-08-03 07:38:27', '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for ai_client_advisor_rel
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_advisor_rel`;
CREATE TABLE `ai_client_advisor_rel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `client_id` bigint(20) NOT NULL COMMENT '客户端ID',
  `advisor_bean_id` bigint(20) NOT NULL COMMENT '顾问Bean ID',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_client_advisor`(`client_id`, `advisor_bean_id`) USING BTREE,
  INDEX `idx_client_id`(`client_id`) USING BTREE,
  INDEX `idx_advisor_bean_id`(`advisor_bean_id`) USING BTREE,
  CONSTRAINT `fk_client_advisor_rel_advisor` FOREIGN KEY (`advisor_bean_id`) REFERENCES `ai_client_advisor` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_client_advisor_rel_client` FOREIGN KEY (`client_id`) REFERENCES `ai_client` (`client_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端与顾问关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_advisor_rel
-- ----------------------------
INSERT INTO `ai_client_advisor_rel` VALUES (1, 1, 1, '2025-08-03 07:38:27');
INSERT INTO `ai_client_advisor_rel` VALUES (2, 1, 2, '2025-08-03 07:38:27');
INSERT INTO `ai_client_advisor_rel` VALUES (3, 2, 2, '2025-08-03 07:38:27');
INSERT INTO `ai_client_advisor_rel` VALUES (4, 2, 3, '2025-08-03 07:38:27');
INSERT INTO `ai_client_advisor_rel` VALUES (5, 3, 1, '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for ai_client_mcp_rel
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_mcp_rel`;
CREATE TABLE `ai_client_mcp_rel`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `client_id` bigint(20) NOT NULL COMMENT '客户端ID',
  `mcp_bean_id` bigint(20) NOT NULL COMMENT 'MCP Bean ID',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_client_mcp`(`client_id`, `mcp_bean_id`) USING BTREE,
  INDEX `idx_client_id`(`client_id`) USING BTREE,
  INDEX `idx_mcp_bean_id`(`mcp_bean_id`) USING BTREE,
  CONSTRAINT `fk_client_mcp_rel_client` FOREIGN KEY (`client_id`) REFERENCES `ai_client` (`client_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_client_mcp_rel_mcp` FOREIGN KEY (`mcp_bean_id`) REFERENCES `ai_client_tool_mcp` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端与MCP工具关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_mcp_rel
-- ----------------------------
INSERT INTO `ai_client_mcp_rel` VALUES (1, 1, 1, '2025-08-03 09:49:50');
INSERT INTO `ai_client_mcp_rel` VALUES (2, 1, 2, '2025-08-03 09:50:02');

-- ----------------------------
-- Table structure for ai_client_model
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_model`;
CREATE TABLE `ai_client_model`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型名称',
  `base_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '基础URL',
  `api_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API密钥',
  `completions_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '/v1/chat/completions' COMMENT '完成路径',
  `embeddings_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '/v1/embeddings' COMMENT '嵌入路径',
  `model_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型类型(openai/azure等)',
  `model_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型版本',
  `timeout` int(11) NULL DEFAULT 60 COMMENT '超时时间(秒)',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_model_name`(`model_name`) USING BTREE,
  INDEX `idx_model_type`(`model_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端模型配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_model
-- ----------------------------
INSERT INTO `ai_client_model` VALUES (1, 'gpt-3.5-turbo', 'https://api.openai.com', 'your-openai-api-key', '/v1/chat/completions', '/v1/embeddings', 'openai', '3.5', 60, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client_model` VALUES (2, 'text-embedding-3-small', 'https://api.openai.com', 'your-openai-api-key', '/v1/chat/completions', '/v1/embeddings', 'openai', '3.0', 60, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client_model` VALUES (3, 'gpt-4', 'https://api.openai.com', 'your-openai-api-key', '/v1/chat/completions', '/v1/embeddings', 'openai', '4.0', 60, '2025-08-03 07:38:27', '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for ai_client_model_tool_config
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_model_tool_config`;
CREATE TABLE `ai_client_model_tool_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `model_id` bigint(20) NOT NULL COMMENT '模型ID',
  `tool_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具类型(mcp/function_call)',
  `tool_id` bigint(20) NOT NULL COMMENT 'MCP ID或function call ID',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_model_id`(`model_id`) USING BTREE,
  INDEX `idx_tool_type_id`(`tool_type`, `tool_id`) USING BTREE,
  CONSTRAINT `fk_model_tool_config_model` FOREIGN KEY (`model_id`) REFERENCES `ai_client_model` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端模型工具配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_model_tool_config
-- ----------------------------
INSERT INTO `ai_client_model_tool_config` VALUES (1, 1, 'mcp', 1, '2025-08-03 09:48:48');
INSERT INTO `ai_client_model_tool_config` VALUES (2, 1, 'mcp', 2, '2025-08-03 09:50:19');

-- ----------------------------
-- Table structure for ai_client_system_prompt
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_system_prompt`;
CREATE TABLE `ai_client_system_prompt`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `prompt_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '提示词内容',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端系统提示词表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_system_prompt
-- ----------------------------
INSERT INTO `ai_client_system_prompt` VALUES (1, '你是一个专业的AI助手，请根据提供的上下文信息回答用户的问题。', '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client_system_prompt` VALUES (2, '你是一个技术专家，专门回答关于Spring AI和RAG技术的问题。', '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_client_system_prompt` VALUES (3, '你是一个友好的客服助手，请耐心回答用户的问题。', '2025-08-03 07:38:27', '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for ai_client_tool_mcp
-- ----------------------------
DROP TABLE IF EXISTS `ai_client_tool_mcp`;
CREATE TABLE `ai_client_tool_mcp`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `mcp_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'MCP名称',
  `transport_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '传输类型(sse/stdio)',
  `transport_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '配置JSON',
  `request_timeout` int(11) NULL DEFAULT 5 COMMENT '请求超时时间(分钟)',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mcp_name`(`mcp_name`) USING BTREE,
  INDEX `idx_transport_type`(`transport_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'AI客户端MCP工具配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_client_tool_mcp
-- ----------------------------
INSERT INTO `ai_client_tool_mcp` VALUES (1, 'weather-tool', 'sse', '{\n	\"baseUri\":\"http://mcp-server-csdn-app:8101\"\n}', 5, '2025-08-03 07:38:27', '2025-08-03 13:48:18');
INSERT INTO `ai_client_tool_mcp` VALUES (2, 'search-tool', 'stdio', '{\n    \"g-search\": {\n        \"command\": \"npx\",\n        \"args\": [\n            \"-y\",\n            \"g-search-mcp\"\n        ]\n    }\n}', 10, '2025-08-03 07:38:27', '2025-08-03 13:48:52');

-- ----------------------------
-- Table structure for ai_rag_knowledge_base
-- ----------------------------
DROP TABLE IF EXISTS `ai_rag_knowledge_base`;
CREATE TABLE `ai_rag_knowledge_base`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '知识库名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '知识库描述',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:启用)',
  `document_count` int(11) NOT NULL DEFAULT 0 COMMENT '文档数量',
  `create_time` datetime NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'RAG知识库元数据表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of ai_rag_knowledge_base
-- ----------------------------
INSERT INTO `ai_rag_knowledge_base` VALUES (1, '技术文档库', 'Spring AI和RAG相关技术文档', 1, 0, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_rag_knowledge_base` VALUES (2, '产品手册库', '产品使用手册和FAQ', 1, 0, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_rag_knowledge_base` VALUES (3, '测试知识库', '用于测试的示例知识库', 1, 0, '2025-08-03 07:38:27', '2025-08-03 07:38:27');
INSERT INTO `ai_rag_knowledge_base` VALUES (4, 'default', '默认知识库', 1, 0, '2025-08-03 07:38:27', '2025-08-03 07:38:27');

-- ----------------------------
-- Table structure for vector_store
-- ----------------------------
DROP TABLE IF EXISTS `vector_store`;
CREATE TABLE `vector_store`  (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `embedding` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of vector_store
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
